"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test/page",{

/***/ "(app-pages-browser)/./src/app/test/page.tsx":
/*!*******************************!*\
  !*** ./src/app/test/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction TestPage() {\n    var _uploadResult_data, _uploadResult_data1, _uploadResult_data2;\n    _s();\n    const [file, setFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadResult, setUploadResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [accountInfo, setAccountInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleFileChange = (e)=>{\n        if (e.target.files && e.target.files[0]) {\n            setFile(e.target.files[0]);\n            setUploadResult(null);\n            setError(null);\n        }\n    };\n    const handleUpload = async ()=>{\n        if (!file) {\n            setError('Please select a file first');\n            return;\n        }\n        setUploading(true);\n        setError(null);\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            const response = await fetch('/api/gofile/upload', {\n                method: 'POST',\n                body: formData\n            });\n            const result = await response.json();\n            if (response.ok) {\n                setUploadResult(result);\n            } else {\n                setError(result.error || 'Upload failed');\n            }\n        } catch (err) {\n            setError('Upload failed: ' + err.message);\n        } finally{\n            setUploading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto bg-white rounded-lg shadow-md p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-gray-900 mb-6 text-center\",\n                    children: \"GoFile.io API Test\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"file\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Select File\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"file\",\n                                    id: \"file\",\n                                    onChange: handleFileChange,\n                                    className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        file && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600\",\n                            children: [\n                                \"Selected: \",\n                                file.name,\n                                \" (\",\n                                (file.size / 1024 / 1024).toFixed(2),\n                                \" MB)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleUpload,\n                            disabled: !file || uploading,\n                            className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed\",\n                            children: uploading ? 'Uploading...' : 'Upload to GoFile.io'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-md p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this),\n                        uploadResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 rounded-md p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-green-800 font-medium mb-2\",\n                                    children: \"Upload Successful!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-green-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"File Code:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 20\n                                                }, this),\n                                                \" \",\n                                                (_uploadResult_data = uploadResult.data) === null || _uploadResult_data === void 0 ? void 0 : _uploadResult_data.code\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Download Link:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 20\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: (_uploadResult_data1 = uploadResult.data) === null || _uploadResult_data1 === void 0 ? void 0 : _uploadResult_data1.directLink,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-blue-600 hover:text-blue-800 break-all\",\n                                            children: (_uploadResult_data2 = uploadResult.data) === null || _uploadResult_data2 === void 0 ? void 0 : _uploadResult_data2.directLink\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\CascadeProjects\\\\FlyFiles\\\\src\\\\app\\\\test\\\\page.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(TestPage, \"2nYKIL+L4Cjo97Rp3p1pZTEJ3AU=\");\n_c = TestPage;\nvar _c;\n$RefreshReg$(_c, \"TestPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test/page.tsx\n"));

/***/ })

});