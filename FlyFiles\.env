
# MongoDB Configuration (used by NextAuth.js and the application)
MONGODB_URI=mongodb+srv://Myckas:<EMAIL>/flyfiles?retryWrites=true&w=majority&appName=Cluster0

# NextAuth.js Configuration
NEXTAUTH_URL=https://flyfiles.mcdevhub.dk  # Update this for production
NEXTAUTH_SECRET=9ca30f4947c68865a7821c79d9783f181ce39e18bb2c0df5ba300c00738b49e2

# Development URL (for local testing)
# NEXTAUTH_URL=http://localhost:3001

# Legacy JWT Secret (still used by some admin routes)
JWT_SECRET=9ca30f4947c68865a7821c79d9783f181ce39e18bb2c0df5ba300c00738b49e2

# Admin API Secret for verification endpoints
ADMIN_API_SECRET=123456789

# Cloudinary configuration (FOR FUTURE USE)
CLOUDINARY_CLOUD_NAME=dwqxk2tip
CLOUDINARY_API_KEY=***************
CLOUDINARY_API_SECRET=rZk3RfvtL1PxPUFT-HplZOKMVTo
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=dwqxk2tip



STRIPE_SECRET_KEY=sk_test_51OlcexGunuvAGRgKFWJL30XibiiWgJ5uPh6SjaRkbwdrSQc5x45LLLW6xUdRzYnBeCyGgLiX3YV74i9uxSIiioxf009GPM9AXe
STRIPE_PUBLISHABLE_KEY=pk_test_51OlcexGunuvAGRgKWPhMq7kylg8AysbnC727bNPKAXBCPUNqWiaRi06UGlItG3Ulba3zOmGK8nb7FRAHqNKHQHY6004eSBlM86
NEXT_PUBLIC_BASE_URL=https://flyfiles.mcdevhub.dk  # Update this for production FOR FUTURE USE

# GoFile.io API Configuration
GOFILE_ACCOUNT_ID=c9dffe78-3881-4a3f-9b65-eaf61ef2482a
GOFILE_ACCOUNT_TOKEN=BYVnXzkJvu4pdQ3GdwDBc3EAdWNz8BSG
GOFILE_API_BASE_URL=https://api.gofile.io
GOFILE_UPLOAD_URL=https://upload.gofile.io

