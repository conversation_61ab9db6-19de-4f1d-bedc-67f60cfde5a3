'use client';

import { useState, useEffect } from 'react';

export default function TestPage() {
  const [files, setFiles] = useState<FileList | null>(null);
  const [folderName, setFolderName] = useState('');
  const [uploading, setUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [accountInfo, setAccountInfo] = useState<any>(null);

  useEffect(() => {
    fetch('/api/gofile/account')
      .then(res => res.json())
      .then(data => setAccountInfo(data))
      .catch(err => console.error('Failed to load account info:', err));
  }, []);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFiles(e.target.files);
      setUploadResult(null);
      setError(null);
    }
  };

  const handleUpload = async () => {
    if (!files || files.length === 0) {
      setError('Please select files first');
      return;
    }

    setUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      
      // Add folder name if provided
      if (folderName) {
        formData.append('folderName', folderName);
      }

      // Add all selected files
      Array.from(files).forEach(file => {
        formData.append('files', file);
      });

      const response = await fetch('/api/gofile/upload', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (response.ok) {
        setUploadResult(result);
      } else {
        setError(result.error || 'Upload failed');
      }
    } catch (err) {
      setError('Upload failed: ' + (err as Error).message);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          GoFile.io API Test
        </h1>

        {accountInfo && (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
            <h3 className="text-blue-800 font-medium mb-2">Account Info</h3>
            <div className="text-sm text-blue-700">
              <p><strong>Status:</strong> {accountInfo.status}</p>
              {accountInfo.data && (
                <>
                  <p><strong>Email:</strong> {accountInfo.data.email}</p>
                  <p><strong>Root Folder:</strong> {accountInfo.data.rootFolder}</p>
                  <p><strong>Tier:</strong> {accountInfo.data.tier}</p>
                </>
              )}
            </div>
          </div>
        )}

        <div className="space-y-4">
          <div>
            <label htmlFor="files" className="block text-sm font-medium text-gray-700 mb-2">
              Select Files or Folder
            </label>
            <input
              type="file"
              id="files"
              onChange={handleFileChange}
              multiple
              // @ts-ignore - webkitdirectory is a non-standard attribute
              webkitdirectory="true"
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
          </div>

          <div>
            <label htmlFor="folderName" className="block text-sm font-medium text-gray-700 mb-2">
              Folder Name (optional)
            </label>
            <input
              type="text"
              id="folderName"
              value={folderName}
              onChange={(e) => setFolderName(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm p-2 border"
              placeholder="Enter folder name"
            />
          </div>

          {files && (
            <div className="text-sm text-gray-600">
              Selected: {files.length} file(s)
              <ul className="mt-1 pl-4 list-disc">
                {Array.from(files).map((file, index) => (
                  <li key={index}>
                    {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                  </li>
                ))}
              </ul>
            </div>
          )}

          <button
            onClick={handleUpload}
            disabled={!files || uploading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {uploading ? 'Uploading...' : 'Upload to GoFile.io'}
          </button>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {uploadResult && (
            <div className="bg-green-50 border border-green-200 rounded-md p-3">
              <h3 className="text-green-800 font-medium mb-2">Upload Successful!</h3>
              <div className="text-sm text-green-700 space-y-1">
                <p><strong>Folder Code:</strong> {uploadResult.data?.code}</p>
                <p><strong>Download Link:</strong></p>
                <a 
                  href={uploadResult.data?.directLink} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 break-all"
                >
                  {uploadResult.data?.directLink}
                </a>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
