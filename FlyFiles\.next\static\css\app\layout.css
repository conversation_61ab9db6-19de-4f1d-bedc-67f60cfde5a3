/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8d697b304b401681-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/ba015fad6dcf6784-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/569ce4b8f30dc480-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Fallback';src: local("Arial");ascent-override: 95.94%;descent-override: 28.16%;line-gap-override: 0.00%;size-adjust: 104.76%
}.__className_5cfdac {font-family: 'Geist', 'Geist Fallback';font-style: normal
}.__variable_5cfdac {--font-geist-sans: 'Geist', 'Geist Fallback'
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/9610d9e46709d722-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/747892c23ea88013-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/93f479601ee12b01-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Mono Fallback';src: local("Arial");ascent-override: 74.67%;descent-override: 21.92%;line-gap-override: 0.00%;size-adjust: 134.59%
}.__className_9a8899 {font-family: 'Geist Mono', 'Geist Mono Fallback';font-style: normal
}.__variable_9a8899 {--font-geist-mono: 'Geist Mono', 'Geist Mono Fallback'
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --color-red-50: oklch(97.1% 0.013 17.38);
    --color-red-100: oklch(93.6% 0.032 17.717);
    --color-red-200: oklch(88.5% 0.062 18.334);
    --color-red-300: oklch(80.8% 0.114 19.571);
    --color-red-400: oklch(70.4% 0.191 22.216);
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-red-800: oklch(44.4% 0.177 26.899);
    --color-orange-50: oklch(98% 0.016 73.684);
    --color-orange-100: oklch(95.4% 0.038 75.164);
    --color-orange-200: oklch(90.1% 0.076 70.697);
    --color-orange-300: oklch(83.7% 0.128 66.29);
    --color-orange-500: oklch(70.5% 0.213 47.604);
    --color-orange-600: oklch(64.6% 0.222 41.116);
    --color-orange-700: oklch(55.3% 0.195 38.402);
    --color-orange-800: oklch(47% 0.157 37.304);
    --color-amber-50: oklch(98.7% 0.022 95.277);
    --color-amber-100: oklch(96.2% 0.059 95.617);
    --color-amber-200: oklch(92.4% 0.12 95.746);
    --color-amber-300: oklch(87.9% 0.169 91.605);
    --color-amber-400: oklch(82.8% 0.189 84.429);
    --color-amber-500: oklch(76.9% 0.188 70.08);
    --color-amber-600: oklch(66.6% 0.179 58.318);
    --color-amber-700: oklch(55.5% 0.163 48.998);
    --color-amber-800: oklch(47.3% 0.137 46.201);
    --color-amber-900: oklch(41.4% 0.112 45.904);
    --color-yellow-50: oklch(98.7% 0.026 102.212);
    --color-yellow-100: oklch(97.3% 0.071 103.193);
    --color-yellow-200: oklch(94.5% 0.129 101.54);
    --color-yellow-300: oklch(90.5% 0.182 98.111);
    --color-yellow-400: oklch(85.2% 0.199 91.936);
    --color-yellow-500: oklch(79.5% 0.184 86.047);
    --color-yellow-600: oklch(68.1% 0.162 75.834);
    --color-yellow-700: oklch(55.4% 0.135 66.442);
    --color-yellow-800: oklch(47.6% 0.114 61.907);
    --color-green-50: oklch(98.2% 0.018 155.826);
    --color-green-100: oklch(96.2% 0.044 156.743);
    --color-green-200: oklch(92.5% 0.084 155.995);
    --color-green-300: oklch(87.1% 0.15 154.449);
    --color-green-400: oklch(79.2% 0.209 151.711);
    --color-green-500: oklch(72.3% 0.219 149.579);
    --color-green-600: oklch(62.7% 0.194 149.214);
    --color-green-700: oklch(52.7% 0.154 150.069);
    --color-green-800: oklch(44.8% 0.119 151.328);
    --color-green-900: oklch(39.3% 0.095 152.535);
    --color-emerald-50: oklch(97.9% 0.021 166.113);
    --color-emerald-100: oklch(95% 0.052 163.051);
    --color-emerald-200: oklch(90.5% 0.093 164.15);
    --color-emerald-400: oklch(76.5% 0.177 163.223);
    --color-emerald-500: oklch(69.6% 0.17 162.48);
    --color-emerald-600: oklch(59.6% 0.145 163.225);
    --color-emerald-700: oklch(50.8% 0.118 165.612);
    --color-emerald-800: oklch(43.2% 0.095 166.913);
    --color-emerald-900: oklch(37.8% 0.077 168.94);
    --color-teal-100: oklch(95.3% 0.051 180.801);
    --color-teal-200: oklch(91% 0.096 180.426);
    --color-teal-300: oklch(85.5% 0.138 181.071);
    --color-teal-400: oklch(77.7% 0.152 181.912);
    --color-teal-500: oklch(70.4% 0.14 182.503);
    --color-teal-600: oklch(60% 0.118 184.704);
    --color-teal-700: oklch(51.1% 0.096 186.391);
    --color-teal-800: oklch(43.7% 0.078 188.216);
    --color-cyan-500: oklch(71.5% 0.143 215.221);
    --color-cyan-600: oklch(60.9% 0.126 221.723);
    --color-sky-50: oklch(97.7% 0.013 236.62);
    --color-sky-100: oklch(95.1% 0.026 236.824);
    --color-sky-400: oklch(74.6% 0.16 232.661);
    --color-sky-600: oklch(58.8% 0.158 241.966);
    --color-sky-700: oklch(50% 0.134 242.749);
    --color-blue-50: oklch(97% 0.014 254.604);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-200: oklch(88.2% 0.059 254.128);
    --color-blue-300: oklch(80.9% 0.105 251.813);
    --color-blue-400: oklch(70.7% 0.165 254.624);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-blue-700: oklch(48.8% 0.243 264.376);
    --color-blue-800: oklch(42.4% 0.199 265.638);
    --color-blue-900: oklch(37.9% 0.146 265.522);
    --color-blue-950: oklch(28.2% 0.091 267.935);
    --color-indigo-50: oklch(96.2% 0.018 272.314);
    --color-indigo-100: oklch(93% 0.034 272.788);
    --color-indigo-200: oklch(87% 0.065 274.039);
    --color-indigo-300: oklch(78.5% 0.115 274.713);
    --color-indigo-400: oklch(67.3% 0.182 276.935);
    --color-indigo-500: oklch(58.5% 0.233 277.117);
    --color-indigo-600: oklch(51.1% 0.262 276.966);
    --color-indigo-700: oklch(45.7% 0.24 277.023);
    --color-indigo-800: oklch(39.8% 0.195 277.366);
    --color-indigo-900: oklch(35.9% 0.144 278.697);
    --color-purple-50: oklch(97.7% 0.014 308.299);
    --color-purple-100: oklch(94.6% 0.033 307.174);
    --color-purple-200: oklch(90.2% 0.063 306.703);
    --color-purple-300: oklch(82.7% 0.119 306.383);
    --color-purple-400: oklch(71.4% 0.203 305.504);
    --color-purple-500: oklch(62.7% 0.265 303.9);
    --color-purple-600: oklch(55.8% 0.288 302.321);
    --color-purple-700: oklch(49.6% 0.265 301.924);
    --color-purple-800: oklch(43.8% 0.218 303.724);
    --color-purple-900: oklch(38.1% 0.176 304.987);
    --color-pink-50: oklch(97.1% 0.014 343.198);
    --color-pink-100: oklch(94.8% 0.028 342.258);
    --color-pink-200: oklch(89.9% 0.061 343.231);
    --color-pink-300: oklch(82.3% 0.12 346.018);
    --color-pink-500: oklch(65.6% 0.241 354.308);
    --color-pink-600: oklch(59.2% 0.249 0.584);
    --color-rose-100: oklch(94.1% 0.03 12.58);
    --color-rose-400: oklch(71.2% 0.194 13.428);
    --color-rose-600: oklch(58.6% 0.253 17.585);
    --color-rose-700: oklch(51.4% 0.222 16.935);
    --color-rose-800: oklch(45.5% 0.188 13.697);
    --color-rose-900: oklch(41% 0.159 10.272);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-light: 300;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --tracking-tight: -0.025em;
    --tracking-wide: 0.025em;
    --tracking-wider: 0.05em;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --drop-shadow-sm: 0 1px 2px rgb(0 0 0 / 0.15);
    --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --animate-bounce: bounce 1s infinite;
    --blur-sm: 8px;
    --blur-md: 12px;
    --blur-xl: 24px;
    --blur-2xl: 40px;
    --blur-3xl: 64px;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .pointer-events-auto {
    pointer-events: auto;
  }
  .pointer-events-none {
    pointer-events: none;
  }
  .collapse {
    visibility: collapse;
  }
  .invisible {
    visibility: hidden;
  }
  .visible {
    visibility: visible;
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-4 {
    inset: calc(var(--spacing) * 4);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .-top-1 {
    top: calc(var(--spacing) * -1);
  }
  .-top-2 {
    top: calc(var(--spacing) * -2);
  }
  .-top-3 {
    top: calc(var(--spacing) * -3);
  }
  .-top-8 {
    top: calc(var(--spacing) * -8);
  }
  .-top-20 {
    top: calc(var(--spacing) * -20);
  }
  .-top-24 {
    top: calc(var(--spacing) * -24);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1 {
    top: calc(var(--spacing) * 1);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-1\/3 {
    top: calc(1/3 * 100%);
  }
  .top-1\/4 {
    top: calc(1/4 * 100%);
  }
  .top-2 {
    top: calc(var(--spacing) * 2);
  }
  .top-2\/4 {
    top: calc(2/4 * 100%);
  }
  .top-3 {
    top: calc(var(--spacing) * 3);
  }
  .top-3\/4 {
    top: calc(3/4 * 100%);
  }
  .top-4 {
    top: calc(var(--spacing) * 4);
  }
  .top-5 {
    top: calc(var(--spacing) * 5);
  }
  .top-6 {
    top: calc(var(--spacing) * 6);
  }
  .top-8 {
    top: calc(var(--spacing) * 8);
  }
  .top-10 {
    top: calc(var(--spacing) * 10);
  }
  .top-16 {
    top: calc(var(--spacing) * 16);
  }
  .top-20 {
    top: calc(var(--spacing) * 20);
  }
  .top-24 {
    top: calc(var(--spacing) * 24);
  }
  .top-28 {
    top: calc(var(--spacing) * 28);
  }
  .top-full {
    top: 100%;
  }
  .-right-1 {
    right: calc(var(--spacing) * -1);
  }
  .-right-2 {
    right: calc(var(--spacing) * -2);
  }
  .-right-4 {
    right: calc(var(--spacing) * -4);
  }
  .-right-6 {
    right: calc(var(--spacing) * -6);
  }
  .-right-8 {
    right: calc(var(--spacing) * -8);
  }
  .-right-20 {
    right: calc(var(--spacing) * -20);
  }
  .-right-24 {
    right: calc(var(--spacing) * -24);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-1\/3 {
    right: calc(1/3 * 100%);
  }
  .right-1\/4 {
    right: calc(1/4 * 100%);
  }
  .right-2 {
    right: calc(var(--spacing) * 2);
  }
  .right-2\/3 {
    right: calc(2/3 * 100%);
  }
  .right-3 {
    right: calc(var(--spacing) * 3);
  }
  .right-4 {
    right: calc(var(--spacing) * 4);
  }
  .right-5 {
    right: calc(var(--spacing) * 5);
  }
  .right-6 {
    right: calc(var(--spacing) * 6);
  }
  .right-8 {
    right: calc(var(--spacing) * 8);
  }
  .right-10 {
    right: calc(var(--spacing) * 10);
  }
  .-bottom-1 {
    bottom: calc(var(--spacing) * -1);
  }
  .-bottom-3 {
    bottom: calc(var(--spacing) * -3);
  }
  .-bottom-4 {
    bottom: calc(var(--spacing) * -4);
  }
  .-bottom-6 {
    bottom: calc(var(--spacing) * -6);
  }
  .-bottom-8 {
    bottom: calc(var(--spacing) * -8);
  }
  .-bottom-10 {
    bottom: calc(var(--spacing) * -10);
  }
  .-bottom-12 {
    bottom: calc(var(--spacing) * -12);
  }
  .-bottom-14 {
    bottom: calc(var(--spacing) * -14);
  }
  .-bottom-16 {
    bottom: calc(var(--spacing) * -16);
  }
  .-bottom-24 {
    bottom: calc(var(--spacing) * -24);
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .bottom-1 {
    bottom: calc(var(--spacing) * 1);
  }
  .bottom-1\/3 {
    bottom: calc(1/3 * 100%);
  }
  .bottom-1\/4 {
    bottom: calc(1/4 * 100%);
  }
  .bottom-3 {
    bottom: calc(var(--spacing) * 3);
  }
  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }
  .bottom-6 {
    bottom: calc(var(--spacing) * 6);
  }
  .bottom-8 {
    bottom: calc(var(--spacing) * 8);
  }
  .bottom-10 {
    bottom: calc(var(--spacing) * 10);
  }
  .bottom-14 {
    bottom: calc(var(--spacing) * 14);
  }
  .bottom-20 {
    bottom: calc(var(--spacing) * 20);
  }
  .bottom-\[-4px\] {
    bottom: -4px;
  }
  .bottom-full {
    bottom: 100%;
  }
  .-left-3 {
    left: calc(var(--spacing) * -3);
  }
  .-left-6 {
    left: calc(var(--spacing) * -6);
  }
  .-left-8 {
    left: calc(var(--spacing) * -8);
  }
  .-left-10 {
    left: calc(var(--spacing) * -10);
  }
  .-left-16 {
    left: calc(var(--spacing) * -16);
  }
  .-left-24 {
    left: calc(var(--spacing) * -24);
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1 {
    left: calc(var(--spacing) * 1);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .left-1\/3 {
    left: calc(1/3 * 100%);
  }
  .left-1\/4 {
    left: calc(1/4 * 100%);
  }
  .left-2 {
    left: calc(var(--spacing) * 2);
  }
  .left-3 {
    left: calc(var(--spacing) * 3);
  }
  .left-4 {
    left: calc(var(--spacing) * 4);
  }
  .left-5 {
    left: calc(var(--spacing) * 5);
  }
  .left-8 {
    left: calc(var(--spacing) * 8);
  }
  .left-10 {
    left: calc(var(--spacing) * 10);
  }
  .left-full {
    left: 100%;
  }
  .-z-10 {
    z-index: calc(10 * -1);
  }
  .-z-20 {
    z-index: calc(20 * -1);
  }
  .z-0 {
    z-index: 0;
  }
  .z-10 {
    z-index: 10;
  }
  .z-20 {
    z-index: 20;
  }
  .z-30 {
    z-index: 30;
  }
  .z-40 {
    z-index: 40;
  }
  .z-50 {
    z-index: 50;
  }
  .z-60 {
    z-index: 60;
  }
  .col-span-full {
    grid-column: 1 / -1;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .m-2 {
    margin: calc(var(--spacing) * 2);
  }
  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }
  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }
  .my-1\.5 {
    margin-block: calc(var(--spacing) * 1.5);
  }
  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }
  .-mt-10 {
    margin-top: calc(var(--spacing) * -10);
  }
  .-mt-16 {
    margin-top: calc(var(--spacing) * -16);
  }
  .-mt-20 {
    margin-top: calc(var(--spacing) * -20);
  }
  .mt-0\.5 {
    margin-top: calc(var(--spacing) * 0.5);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }
  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }
  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }
  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }
  .mt-auto {
    margin-top: auto;
  }
  .-mr-1 {
    margin-right: calc(var(--spacing) * -1);
  }
  .-mr-2 {
    margin-right: calc(var(--spacing) * -2);
  }
  .-mr-10 {
    margin-right: calc(var(--spacing) * -10);
  }
  .-mr-16 {
    margin-right: calc(var(--spacing) * -16);
  }
  .-mr-20 {
    margin-right: calc(var(--spacing) * -20);
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-1\.5 {
    margin-right: calc(var(--spacing) * 1.5);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }
  .mr-6 {
    margin-right: calc(var(--spacing) * 6);
  }
  .-mb-12 {
    margin-bottom: calc(var(--spacing) * -12);
  }
  .-mb-20 {
    margin-bottom: calc(var(--spacing) * -20);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-7 {
    margin-bottom: calc(var(--spacing) * 7);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }
  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }
  .mb-20 {
    margin-bottom: calc(var(--spacing) * 20);
  }
  .-ml-0\.5 {
    margin-left: calc(var(--spacing) * -0.5);
  }
  .-ml-1 {
    margin-left: calc(var(--spacing) * -1);
  }
  .-ml-10 {
    margin-left: calc(var(--spacing) * -10);
  }
  .-ml-12 {
    margin-left: calc(var(--spacing) * -12);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-1\.5 {
    margin-left: calc(var(--spacing) * 1.5);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }
  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }
  .ml-5 {
    margin-left: calc(var(--spacing) * 5);
  }
  .ml-6 {
    margin-left: calc(var(--spacing) * 6);
  }
  .ml-8 {
    margin-left: calc(var(--spacing) * 8);
  }
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .block {
    display: block;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .table {
    display: table;
  }
  .aspect-\[4\/3\] {
    aspect-ratio: 4/3;
  }
  .aspect-\[16\/9\] {
    aspect-ratio: 16/9;
  }
  .h-0\.5 {
    height: calc(var(--spacing) * 0.5);
  }
  .h-1 {
    height: calc(var(--spacing) * 1);
  }
  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }
  .h-1\/2 {
    height: calc(1/2 * 100%);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-3\.5 {
    height: calc(var(--spacing) * 3.5);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-7 {
    height: calc(var(--spacing) * 7);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-9 {
    height: calc(var(--spacing) * 9);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-14 {
    height: calc(var(--spacing) * 14);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-20 {
    height: calc(var(--spacing) * 20);
  }
  .h-24 {
    height: calc(var(--spacing) * 24);
  }
  .h-28 {
    height: calc(var(--spacing) * 28);
  }
  .h-32 {
    height: calc(var(--spacing) * 32);
  }
  .h-36 {
    height: calc(var(--spacing) * 36);
  }
  .h-40 {
    height: calc(var(--spacing) * 40);
  }
  .h-48 {
    height: calc(var(--spacing) * 48);
  }
  .h-52 {
    height: calc(var(--spacing) * 52);
  }
  .h-60 {
    height: calc(var(--spacing) * 60);
  }
  .h-64 {
    height: calc(var(--spacing) * 64);
  }
  .h-72 {
    height: calc(var(--spacing) * 72);
  }
  .h-80 {
    height: calc(var(--spacing) * 80);
  }
  .h-96 {
    height: calc(var(--spacing) * 96);
  }
  .h-\[300px\] {
    height: 300px;
  }
  .h-\[800px\] {
    height: 800px;
  }
  .h-full {
    height: 100%;
  }
  .max-h-0 {
    max-height: calc(var(--spacing) * 0);
  }
  .max-h-60 {
    max-height: calc(var(--spacing) * 60);
  }
  .max-h-80 {
    max-height: calc(var(--spacing) * 80);
  }
  .max-h-\[90vh\] {
    max-height: 90vh;
  }
  .max-h-\[500px\] {
    max-height: 500px;
  }
  .min-h-\[1em\] {
    min-height: 1em;
  }
  .min-h-\[2\.5rem\] {
    min-height: 2.5rem;
  }
  .min-h-\[8rem\] {
    min-height: 8rem;
  }
  .min-h-\[200px\] {
    min-height: 200px;
  }
  .min-h-\[300px\] {
    min-height: 300px;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-0 {
    width: calc(var(--spacing) * 0);
  }
  .w-1\.5 {
    width: calc(var(--spacing) * 1.5);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-3\.5 {
    width: calc(var(--spacing) * 3.5);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-7 {
    width: calc(var(--spacing) * 7);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-9 {
    width: calc(var(--spacing) * 9);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-14 {
    width: calc(var(--spacing) * 14);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-28 {
    width: calc(var(--spacing) * 28);
  }
  .w-32 {
    width: calc(var(--spacing) * 32);
  }
  .w-40 {
    width: calc(var(--spacing) * 40);
  }
  .w-48 {
    width: calc(var(--spacing) * 48);
  }
  .w-52 {
    width: calc(var(--spacing) * 52);
  }
  .w-60 {
    width: calc(var(--spacing) * 60);
  }
  .w-64 {
    width: calc(var(--spacing) * 64);
  }
  .w-72 {
    width: calc(var(--spacing) * 72);
  }
  .w-80 {
    width: calc(var(--spacing) * 80);
  }
  .w-96 {
    width: calc(var(--spacing) * 96);
  }
  .w-\[120\%\] {
    width: 120%;
  }
  .w-\[800px\] {
    width: 800px;
  }
  .w-auto {
    width: auto;
  }
  .w-full {
    width: 100%;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-3xl {
    max-width: var(--container-3xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-5xl {
    max-width: var(--container-5xl);
  }
  .max-w-6xl {
    max-width: var(--container-6xl);
  }
  .max-w-7xl {
    max-width: var(--container-7xl);
  }
  .max-w-\[65\%\] {
    max-width: 65%;
  }
  .max-w-\[180px\] {
    max-width: 180px;
  }
  .max-w-lg {
    max-width: var(--container-lg);
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .max-w-none {
    max-width: none;
  }
  .max-w-prose {
    max-width: 65ch;
  }
  .max-w-sm {
    max-width: var(--container-sm);
  }
  .max-w-xl {
    max-width: var(--container-xl);
  }
  .max-w-xs {
    max-width: var(--container-xs);
  }
  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }
  .min-w-full {
    min-width: 100%;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .shrink-0 {
    flex-shrink: 0;
  }
  .flex-grow {
    flex-grow: 1;
  }
  .border-collapse {
    border-collapse: collapse;
  }
  .origin-center {
    transform-origin: center;
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-0 {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-1\/2 {
    --tw-translate-x: calc(1/2 * 100%);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-4 {
    --tw-translate-x: calc(var(--spacing) * 4);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-6 {
    --tw-translate-x: calc(var(--spacing) * 6);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/3 {
    --tw-translate-y: calc(calc(1/3 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-2 {
    --tw-translate-y: calc(var(--spacing) * -2);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-0 {
    --tw-translate-y: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-2 {
    --tw-translate-y: calc(var(--spacing) * 2);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-10 {
    --tw-translate-y: calc(var(--spacing) * 10);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .scale-0 {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .scale-105 {
    --tw-scale-x: 105%;
    --tw-scale-y: 105%;
    --tw-scale-z: 105%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .scale-125 {
    --tw-scale-x: 125%;
    --tw-scale-y: 125%;
    --tw-scale-z: 125%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .-rotate-45 {
    rotate: calc(45deg * -1);
  }
  .rotate-0 {
    rotate: 0deg;
  }
  .rotate-2 {
    rotate: 2deg;
  }
  .rotate-45 {
    rotate: 45deg;
  }
  .rotate-90 {
    rotate: 90deg;
  }
  .rotate-180 {
    rotate: 180deg;
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .animate-\[blink_1s_infinite\] {
    animation: blink 1s infinite;
  }
  .animate-\[countUp_1s_ease-in-out_10\.5s_forwards\] {
    animation: countUp 1s ease-in-out 10.5s forwards;
  }
  .animate-\[fadeIn_0\.2s_ease-in-out_0\.1s_forwards\] {
    animation: fadeIn 0.2s ease-in-out 0.1s forwards;
  }
  .animate-\[fadeIn_0\.2s_ease-in-out_0\.6s_forwards\] {
    animation: fadeIn 0.2s ease-in-out 0.6s forwards;
  }
  .animate-\[fadeIn_0\.2s_ease-in-out_1\.2s_forwards\] {
    animation: fadeIn 0.2s ease-in-out 1.2s forwards;
  }
  .animate-\[fadeIn_0\.2s_ease-in-out_1\.8s_forwards\] {
    animation: fadeIn 0.2s ease-in-out 1.8s forwards;
  }
  .animate-\[fadeIn_0\.2s_ease-in-out_2\.4s_forwards\] {
    animation: fadeIn 0.2s ease-in-out 2.4s forwards;
  }
  .animate-\[fadeIn_0\.2s_ease-in-out_3\.0s_forwards\] {
    animation: fadeIn 0.2s ease-in-out 3.0s forwards;
  }
  .animate-\[fadeIn_0\.2s_ease-in-out_3\.6s_forwards\] {
    animation: fadeIn 0.2s ease-in-out 3.6s forwards;
  }
  .animate-\[fadeIn_0\.2s_ease-in-out_4\.2s_forwards\] {
    animation: fadeIn 0.2s ease-in-out 4.2s forwards;
  }
  .animate-\[fadeIn_0\.2s_ease-in-out_5\.7s_forwards\] {
    animation: fadeIn 0.2s ease-in-out 5.7s forwards;
  }
  .animate-\[fadeIn_0\.2s_ease-in-out_6\.2s_forwards\] {
    animation: fadeIn 0.2s ease-in-out 6.2s forwards;
  }
  .animate-\[fadeIn_0\.2s_ease-in-out_8\.2s_forwards\] {
    animation: fadeIn 0.2s ease-in-out 8.2s forwards;
  }
  .animate-\[fadeIn_0\.2s_ease-in-out_8\.7s_forwards\] {
    animation: fadeIn 0.2s ease-in-out 8.7s forwards;
  }
  .animate-\[fadeIn_0\.2s_ease-in-out_9\.2s_forwards\] {
    animation: fadeIn 0.2s ease-in-out 9.2s forwards;
  }
  .animate-\[fadeIn_0\.2s_ease-in-out_10\.0s_forwards\] {
    animation: fadeIn 0.2s ease-in-out 10.0s forwards;
  }
  .animate-\[fadeIn_0\.2s_ease-in-out_11\.0s_forwards\] {
    animation: fadeIn 0.2s ease-in-out 11.0s forwards;
  }
  .animate-\[pulse_1s_ease-in-out_4\.2s_forwards\] {
    animation: pulse 1s ease-in-out 4.2s forwards;
  }
  .animate-\[pulse_1s_ease-in-out_4\.7s_forwards\] {
    animation: pulse 1s ease-in-out 4.7s forwards;
  }
  .animate-\[pulse_1s_ease-in-out_5\.2s_forwards\] {
    animation: pulse 1s ease-in-out 5.2s forwards;
  }
  .animate-\[pulse_1s_ease-in-out_6\.7s_forwards\] {
    animation: pulse 1s ease-in-out 6.7s forwards;
  }
  .animate-\[pulse_1s_ease-in-out_7\.2s_forwards\] {
    animation: pulse 1s ease-in-out 7.2s forwards;
  }
  .animate-\[pulse_1s_ease-in-out_7\.7s_forwards\] {
    animation: pulse 1s ease-in-out 7.7s forwards;
  }
  .animate-bounce {
    animation: var(--animate-bounce);
  }
  .animate-ping {
    animation: var(--animate-ping);
  }
  .animate-pulse {
    animation: var(--animate-pulse);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-default {
    cursor: default;
  }
  .cursor-move {
    cursor: move;
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .cursor-text {
    cursor: text;
  }
  .resize {
    resize: both;
  }
  .resize-y {
    resize: vertical;
  }
  .scroll-mt-24 {
    scroll-margin-top: calc(var(--spacing) * 24);
  }
  .list-inside {
    list-style-position: inside;
  }
  .list-decimal {
    list-style-type: decimal;
  }
  .list-disc {
    list-style-type: disc;
  }
  .appearance-none {
    appearance: none;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .gap-0\.5 {
    gap: calc(var(--spacing) * 0.5);
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .gap-10 {
    gap: calc(var(--spacing) * 10);
  }
  .gap-12 {
    gap: calc(var(--spacing) * 12);
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-1\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-7 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 7) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 7) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-8 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-12 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 12) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .gap-x-6 {
    column-gap: calc(var(--spacing) * 6);
  }
  .-space-x-px {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(-1px * var(--tw-space-x-reverse));
      margin-inline-end: calc(-1px * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-6 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-8 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .gap-y-1 {
    row-gap: calc(var(--spacing) * 1);
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-gray-100 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-100);
    }
  }
  .divide-gray-200 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-200);
    }
  }
  .self-end {
    align-self: flex-end;
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-sm {
    border-radius: var(--radius-sm);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-t-2xl {
    border-top-left-radius: var(--radius-2xl);
    border-top-right-radius: var(--radius-2xl);
  }
  .rounded-t-lg {
    border-top-left-radius: var(--radius-lg);
    border-top-right-radius: var(--radius-lg);
  }
  .rounded-l-md {
    border-top-left-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
  }
  .rounded-r-full {
    border-top-right-radius: calc(infinity * 1px);
    border-bottom-right-radius: calc(infinity * 1px);
  }
  .rounded-r-lg {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
  }
  .rounded-r-md {
    border-top-right-radius: var(--radius-md);
    border-bottom-right-radius: var(--radius-md);
  }
  .rounded-bl-\[3rem\] {
    border-bottom-left-radius: 3rem;
  }
  .rounded-bl-\[5rem\] {
    border-bottom-left-radius: 5rem;
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }
  .border-\[3px\] {
    border-style: var(--tw-border-style);
    border-width: 3px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-t-2 {
    border-top-style: var(--tw-border-style);
    border-top-width: 2px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .border-none {
    --tw-border-style: none;
    border-style: none;
  }
  .border-amber-100 {
    border-color: var(--color-amber-100);
  }
  .border-amber-200 {
    border-color: var(--color-amber-200);
  }
  .border-amber-500 {
    border-color: var(--color-amber-500);
  }
  .border-blue-100 {
    border-color: var(--color-blue-100);
  }
  .border-blue-200 {
    border-color: var(--color-blue-200);
  }
  .border-blue-300 {
    border-color: var(--color-blue-300);
  }
  .border-blue-400 {
    border-color: var(--color-blue-400);
  }
  .border-blue-400\/20 {
    border-color: color-mix(in srgb, oklch(70.7% 0.165 254.624) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-blue-400) 20%, transparent);
    }
  }
  .border-blue-400\/30 {
    border-color: color-mix(in srgb, oklch(70.7% 0.165 254.624) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-blue-400) 30%, transparent);
    }
  }
  .border-blue-500 {
    border-color: var(--color-blue-500);
  }
  .border-blue-600 {
    border-color: var(--color-blue-600);
  }
  .border-blue-800\/50 {
    border-color: color-mix(in srgb, oklch(42.4% 0.199 265.638) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-blue-800) 50%, transparent);
    }
  }
  .border-blue-900 {
    border-color: var(--color-blue-900);
  }
  .border-emerald-100 {
    border-color: var(--color-emerald-100);
  }
  .border-gray-100 {
    border-color: var(--color-gray-100);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-200\/50 {
    border-color: color-mix(in srgb, oklch(92.8% 0.006 264.531) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-gray-200) 50%, transparent);
    }
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-gray-400 {
    border-color: var(--color-gray-400);
  }
  .border-gray-500 {
    border-color: var(--color-gray-500);
  }
  .border-gray-600 {
    border-color: var(--color-gray-600);
  }
  .border-gray-700 {
    border-color: var(--color-gray-700);
  }
  .border-green-100 {
    border-color: var(--color-green-100);
  }
  .border-green-200 {
    border-color: var(--color-green-200);
  }
  .border-green-300 {
    border-color: var(--color-green-300);
  }
  .border-green-400\/30 {
    border-color: color-mix(in srgb, oklch(79.2% 0.209 151.711) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-green-400) 30%, transparent);
    }
  }
  .border-green-500 {
    border-color: var(--color-green-500);
  }
  .border-indigo-100 {
    border-color: var(--color-indigo-100);
  }
  .border-indigo-400 {
    border-color: var(--color-indigo-400);
  }
  .border-indigo-500 {
    border-color: var(--color-indigo-500);
  }
  .border-orange-100 {
    border-color: var(--color-orange-100);
  }
  .border-orange-500 {
    border-color: var(--color-orange-500);
  }
  .border-purple-100 {
    border-color: var(--color-purple-100);
  }
  .border-purple-200 {
    border-color: var(--color-purple-200);
  }
  .border-purple-400 {
    border-color: var(--color-purple-400);
  }
  .border-purple-400\/20 {
    border-color: color-mix(in srgb, oklch(71.4% 0.203 305.504) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-purple-400) 20%, transparent);
    }
  }
  .border-purple-500 {
    border-color: var(--color-purple-500);
  }
  .border-purple-700 {
    border-color: var(--color-purple-700);
  }
  .border-red-100 {
    border-color: var(--color-red-100);
  }
  .border-red-200 {
    border-color: var(--color-red-200);
  }
  .border-red-300 {
    border-color: var(--color-red-300);
  }
  .border-red-400 {
    border-color: var(--color-red-400);
  }
  .border-red-500 {
    border-color: var(--color-red-500);
  }
  .border-red-600 {
    border-color: var(--color-red-600);
  }
  .border-rose-100 {
    border-color: var(--color-rose-100);
  }
  .border-sky-100 {
    border-color: var(--color-sky-100);
  }
  .border-teal-400 {
    border-color: var(--color-teal-400);
  }
  .border-teal-500\/30 {
    border-color: color-mix(in srgb, oklch(70.4% 0.14 182.503) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-teal-500) 30%, transparent);
    }
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-white {
    border-color: var(--color-white);
  }
  .border-white\/10 {
    border-color: color-mix(in srgb, #fff 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }
  .border-white\/20 {
    border-color: color-mix(in srgb, #fff 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }
  .border-white\/30 {
    border-color: color-mix(in srgb, #fff 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-white) 30%, transparent);
    }
  }
  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }
  .border-yellow-300 {
    border-color: var(--color-yellow-300);
  }
  .border-yellow-400 {
    border-color: var(--color-yellow-400);
  }
  .border-yellow-500 {
    border-color: var(--color-yellow-500);
  }
  .border-t-blue-100 {
    border-top-color: var(--color-blue-100);
  }
  .border-t-blue-600 {
    border-top-color: var(--color-blue-600);
  }
  .border-t-gray-900 {
    border-top-color: var(--color-gray-900);
  }
  .border-t-transparent {
    border-top-color: transparent;
  }
  .bg-\[\#1e1e1e\] {
    background-color: #1e1e1e;
  }
  .bg-\[\#61c554\] {
    background-color: #61c554;
  }
  .bg-\[\#5865F2\] {
    background-color: #5865F2;
  }
  .bg-\[\#333333\] {
    background-color: #333333;
  }
  .bg-\[\#ec6a5f\] {
    background-color: #ec6a5f;
  }
  .bg-\[\#f4bf4f\] {
    background-color: #f4bf4f;
  }
  .bg-amber-50 {
    background-color: var(--color-amber-50);
  }
  .bg-amber-100 {
    background-color: var(--color-amber-100);
  }
  .bg-amber-200 {
    background-color: var(--color-amber-200);
  }
  .bg-amber-300 {
    background-color: var(--color-amber-300);
  }
  .bg-amber-400 {
    background-color: var(--color-amber-400);
  }
  .bg-amber-500 {
    background-color: var(--color-amber-500);
  }
  .bg-amber-500\/90 {
    background-color: color-mix(in srgb, oklch(76.9% 0.188 70.08) 90%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-amber-500) 90%, transparent);
    }
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-black\/30 {
    background-color: color-mix(in srgb, #000 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
  }
  .bg-black\/40 {
    background-color: color-mix(in srgb, #000 40%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 40%, transparent);
    }
  }
  .bg-black\/50 {
    background-color: color-mix(in srgb, #000 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }
  .bg-black\/70 {
    background-color: color-mix(in srgb, #000 70%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 70%, transparent);
    }
  }
  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }
  .bg-blue-50\/50 {
    background-color: color-mix(in srgb, oklch(97% 0.014 254.604) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-50) 50%, transparent);
    }
  }
  .bg-blue-50\/70 {
    background-color: color-mix(in srgb, oklch(97% 0.014 254.604) 70%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-50) 70%, transparent);
    }
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-100\/20 {
    background-color: color-mix(in srgb, oklch(93.2% 0.032 255.585) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-100) 20%, transparent);
    }
  }
  .bg-blue-100\/50 {
    background-color: color-mix(in srgb, oklch(93.2% 0.032 255.585) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-100) 50%, transparent);
    }
  }
  .bg-blue-200 {
    background-color: var(--color-blue-200);
  }
  .bg-blue-300 {
    background-color: var(--color-blue-300);
  }
  .bg-blue-400 {
    background-color: var(--color-blue-400);
  }
  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }
  .bg-blue-500\/90 {
    background-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 90%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-500) 90%, transparent);
    }
  }
  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }
  .bg-blue-600\/10 {
    background-color: color-mix(in srgb, oklch(54.6% 0.245 262.881) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-600) 10%, transparent);
    }
  }
  .bg-blue-600\/20 {
    background-color: color-mix(in srgb, oklch(54.6% 0.245 262.881) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-600) 20%, transparent);
    }
  }
  .bg-blue-600\/30 {
    background-color: color-mix(in srgb, oklch(54.6% 0.245 262.881) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-600) 30%, transparent);
    }
  }
  .bg-blue-600\/40 {
    background-color: color-mix(in srgb, oklch(54.6% 0.245 262.881) 40%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-600) 40%, transparent);
    }
  }
  .bg-blue-700 {
    background-color: var(--color-blue-700);
  }
  .bg-blue-800 {
    background-color: var(--color-blue-800);
  }
  .bg-blue-800\/30 {
    background-color: color-mix(in srgb, oklch(42.4% 0.199 265.638) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-blue-800) 30%, transparent);
    }
  }
  .bg-blue-900 {
    background-color: var(--color-blue-900);
  }
  .bg-cyan-500 {
    background-color: var(--color-cyan-500);
  }
  .bg-cyan-600\/20 {
    background-color: color-mix(in srgb, oklch(60.9% 0.126 221.723) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-cyan-600) 20%, transparent);
    }
  }
  .bg-emerald-50 {
    background-color: var(--color-emerald-50);
  }
  .bg-emerald-100 {
    background-color: var(--color-emerald-100);
  }
  .bg-emerald-400 {
    background-color: var(--color-emerald-400);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }
  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }
  .bg-gray-500 {
    background-color: var(--color-gray-500);
  }
  .bg-gray-500\/50 {
    background-color: color-mix(in srgb, oklch(55.1% 0.027 264.364) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-gray-500) 50%, transparent);
    }
  }
  .bg-gray-500\/90 {
    background-color: color-mix(in srgb, oklch(55.1% 0.027 264.364) 90%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-gray-500) 90%, transparent);
    }
  }
  .bg-gray-700 {
    background-color: var(--color-gray-700);
  }
  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }
  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }
  .bg-green-50 {
    background-color: var(--color-green-50);
  }
  .bg-green-100 {
    background-color: var(--color-green-100);
  }
  .bg-green-400 {
    background-color: var(--color-green-400);
  }
  .bg-green-500 {
    background-color: var(--color-green-500);
  }
  .bg-green-500\/80 {
    background-color: color-mix(in srgb, oklch(72.3% 0.219 149.579) 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-green-500) 80%, transparent);
    }
  }
  .bg-green-500\/90 {
    background-color: color-mix(in srgb, oklch(72.3% 0.219 149.579) 90%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-green-500) 90%, transparent);
    }
  }
  .bg-green-600 {
    background-color: var(--color-green-600);
  }
  .bg-indigo-50 {
    background-color: var(--color-indigo-50);
  }
  .bg-indigo-50\/50 {
    background-color: color-mix(in srgb, oklch(96.2% 0.018 272.314) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-indigo-50) 50%, transparent);
    }
  }
  .bg-indigo-100 {
    background-color: var(--color-indigo-100);
  }
  .bg-indigo-200 {
    background-color: var(--color-indigo-200);
  }
  .bg-indigo-500 {
    background-color: var(--color-indigo-500);
  }
  .bg-indigo-600 {
    background-color: var(--color-indigo-600);
  }
  .bg-indigo-600\/20 {
    background-color: color-mix(in srgb, oklch(51.1% 0.262 276.966) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-indigo-600) 20%, transparent);
    }
  }
  .bg-indigo-600\/80 {
    background-color: color-mix(in srgb, oklch(51.1% 0.262 276.966) 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-indigo-600) 80%, transparent);
    }
  }
  .bg-orange-100 {
    background-color: var(--color-orange-100);
  }
  .bg-orange-500 {
    background-color: var(--color-orange-500);
  }
  .bg-pink-500 {
    background-color: var(--color-pink-500);
  }
  .bg-pink-500\/90 {
    background-color: color-mix(in srgb, oklch(65.6% 0.241 354.308) 90%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-pink-500) 90%, transparent);
    }
  }
  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }
  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }
  .bg-purple-400 {
    background-color: var(--color-purple-400);
  }
  .bg-purple-500 {
    background-color: var(--color-purple-500);
  }
  .bg-purple-500\/90 {
    background-color: color-mix(in srgb, oklch(62.7% 0.265 303.9) 90%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-purple-500) 90%, transparent);
    }
  }
  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }
  .bg-purple-600\/20 {
    background-color: color-mix(in srgb, oklch(55.8% 0.288 302.321) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-purple-600) 20%, transparent);
    }
  }
  .bg-red-50 {
    background-color: var(--color-red-50);
  }
  .bg-red-100 {
    background-color: var(--color-red-100);
  }
  .bg-red-100\/50 {
    background-color: color-mix(in srgb, oklch(93.6% 0.032 17.717) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-red-100) 50%, transparent);
    }
  }
  .bg-red-400 {
    background-color: var(--color-red-400);
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-red-600 {
    background-color: var(--color-red-600);
  }
  .bg-red-700 {
    background-color: var(--color-red-700);
  }
  .bg-rose-100 {
    background-color: var(--color-rose-100);
  }
  .bg-rose-400 {
    background-color: var(--color-rose-400);
  }
  .bg-sky-100 {
    background-color: var(--color-sky-100);
  }
  .bg-sky-600\/20 {
    background-color: color-mix(in srgb, oklch(58.8% 0.158 241.966) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-sky-600) 20%, transparent);
    }
  }
  .bg-teal-500 {
    background-color: var(--color-teal-500);
  }
  .bg-teal-500\/10 {
    background-color: color-mix(in srgb, oklch(70.4% 0.14 182.503) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-teal-500) 10%, transparent);
    }
  }
  .bg-teal-500\/30 {
    background-color: color-mix(in srgb, oklch(70.4% 0.14 182.503) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-teal-500) 30%, transparent);
    }
  }
  .bg-teal-600\/20 {
    background-color: color-mix(in srgb, oklch(60% 0.118 184.704) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-teal-600) 20%, transparent);
    }
  }
  .bg-teal-800\/50 {
    background-color: color-mix(in srgb, oklch(43.7% 0.078 188.216) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-teal-800) 50%, transparent);
    }
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-white\/5 {
    background-color: color-mix(in srgb, #fff 5%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }
  .bg-white\/10 {
    background-color: color-mix(in srgb, #fff 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }
  .bg-white\/15 {
    background-color: color-mix(in srgb, #fff 15%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 15%, transparent);
    }
  }
  .bg-white\/20 {
    background-color: color-mix(in srgb, #fff 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }
  .bg-white\/50 {
    background-color: color-mix(in srgb, #fff 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
    }
  }
  .bg-white\/60 {
    background-color: color-mix(in srgb, #fff 60%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 60%, transparent);
    }
  }
  .bg-white\/70 {
    background-color: color-mix(in srgb, #fff 70%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 70%, transparent);
    }
  }
  .bg-white\/80 {
    background-color: color-mix(in srgb, #fff 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 80%, transparent);
    }
  }
  .bg-white\/90 {
    background-color: color-mix(in srgb, #fff 90%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }
  .bg-white\/95 {
    background-color: color-mix(in srgb, #fff 95%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 95%, transparent);
    }
  }
  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }
  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }
  .bg-yellow-300 {
    background-color: var(--color-yellow-300);
  }
  .bg-yellow-400 {
    background-color: var(--color-yellow-400);
  }
  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }
  .bg-yellow-600 {
    background-color: var(--color-yellow-600);
  }
  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-tl {
    --tw-gradient-position: to top left in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-tr {
    --tw-gradient-position: to top right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-\[radial-gradient\(circle_at_center\,rgba\(99\,102\,241\,0\.15\)\,transparent_35\%\)\,radial-gradient\(circle_at_25\%_25\%\,rgba\(79\,70\,229\,0\.15\)\,transparent_25\%\)\,radial-gradient\(circle_at_75\%_75\%\,rgba\(59\,130\,246\,0\.15\)\,transparent_20\%\)\] {
    background-image: radial-gradient(circle at center,rgba(99,102,241,0.15),transparent 35%),radial-gradient(circle at 25% 25%,rgba(79,70,229,0.15),transparent 25%),radial-gradient(circle at 75% 75%,rgba(59,130,246,0.15),transparent 20%);
  }
  .bg-\[url\(\'\/about-pattern\.svg\'\)\] {
    background-image: url('/about-pattern.svg');
  }
  .bg-\[url\(\'\/cta-pattern\.svg\'\)\] {
    background-image: url('/cta-pattern.svg');
  }
  .bg-\[url\(\'\/home-pattern\.svg\'\)\] {
    background-image: url('/home-pattern.svg');
  }
  .bg-\[url\(\'\/images\/pattern\.svg\'\)\] {
    background-image: url('/images/pattern.svg');
  }
  .bg-\[url\(\'\/pattern-dots\.svg\'\)\] {
    background-image: url('/pattern-dots.svg');
  }
  .bg-\[url\(\'\/pattern-grid\.svg\'\)\] {
    background-image: url('/pattern-grid.svg');
  }
  .bg-\[url\(\'\/privacy-pattern\.svg\'\)\] {
    background-image: url('/privacy-pattern.svg');
  }
  .bg-\[url\(\'\/tos-pattern\.svg\'\)\] {
    background-image: url('/tos-pattern.svg');
  }
  .from-\[\#5865F2\] {
    --tw-gradient-from: #5865F2;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-amber-400 {
    --tw-gradient-from: var(--color-amber-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-50 {
    --tw-gradient-from: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-100\/40 {
    --tw-gradient-from: color-mix(in srgb, oklch(93.2% 0.032 255.585) 40%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-100) 40%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-200 {
    --tw-gradient-from: var(--color-blue-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-300\/15 {
    --tw-gradient-from: color-mix(in srgb, oklch(80.9% 0.105 251.813) 15%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-300) 15%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-400 {
    --tw-gradient-from: var(--color-blue-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-400\/10 {
    --tw-gradient-from: color-mix(in srgb, oklch(70.7% 0.165 254.624) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-400) 10%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-500 {
    --tw-gradient-from: var(--color-blue-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-500\/20 {
    --tw-gradient-from: color-mix(in srgb, oklch(62.3% 0.214 259.815) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-600 {
    --tw-gradient-from: var(--color-blue-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-700 {
    --tw-gradient-from: var(--color-blue-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-800 {
    --tw-gradient-from: var(--color-blue-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-900 {
    --tw-gradient-from: var(--color-blue-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-50 {
    --tw-gradient-from: var(--color-gray-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-100 {
    --tw-gradient-from: var(--color-gray-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-400 {
    --tw-gradient-from: var(--color-gray-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-800 {
    --tw-gradient-from: var(--color-gray-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-green-50 {
    --tw-gradient-from: var(--color-green-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-green-100 {
    --tw-gradient-from: var(--color-green-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-green-200 {
    --tw-gradient-from: var(--color-green-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-green-600 {
    --tw-gradient-from: var(--color-green-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-indigo-50 {
    --tw-gradient-from: var(--color-indigo-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-indigo-100 {
    --tw-gradient-from: var(--color-indigo-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-indigo-400 {
    --tw-gradient-from: var(--color-indigo-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-indigo-400\/10 {
    --tw-gradient-from: color-mix(in srgb, oklch(67.3% 0.182 276.935) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-indigo-400) 10%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-indigo-500 {
    --tw-gradient-from: var(--color-indigo-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-indigo-600 {
    --tw-gradient-from: var(--color-indigo-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-orange-50 {
    --tw-gradient-from: var(--color-orange-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-purple-50 {
    --tw-gradient-from: var(--color-purple-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-purple-100 {
    --tw-gradient-from: var(--color-purple-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-purple-200 {
    --tw-gradient-from: var(--color-purple-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-purple-400 {
    --tw-gradient-from: var(--color-purple-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-purple-600 {
    --tw-gradient-from: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-red-50 {
    --tw-gradient-from: var(--color-red-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-red-100 {
    --tw-gradient-from: var(--color-red-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-red-200 {
    --tw-gradient-from: var(--color-red-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-red-500 {
    --tw-gradient-from: var(--color-red-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-red-600 {
    --tw-gradient-from: var(--color-red-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-sky-50 {
    --tw-gradient-from: var(--color-sky-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-sky-400 {
    --tw-gradient-from: var(--color-sky-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-teal-400 {
    --tw-gradient-from: var(--color-teal-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-teal-600 {
    --tw-gradient-from: var(--color-teal-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-white {
    --tw-gradient-from: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-white\/10 {
    --tw-gradient-from: color-mix(in srgb, #fff 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-yellow-300 {
    --tw-gradient-from: var(--color-yellow-300);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-yellow-400 {
    --tw-gradient-from: var(--color-yellow-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-yellow-500 {
    --tw-gradient-from: var(--color-yellow-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .via-blue-50 {
    --tw-gradient-via: var(--color-blue-50);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-blue-700 {
    --tw-gradient-via: var(--color-blue-700);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-blue-800 {
    --tw-gradient-via: var(--color-blue-800);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-indigo-50 {
    --tw-gradient-via: var(--color-indigo-50);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-indigo-600 {
    --tw-gradient-via: var(--color-indigo-600);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .via-purple-600 {
    --tw-gradient-via: var(--color-purple-600);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }
  .to-\[\#404EED\] {
    --tw-gradient-to: #404EED;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-amber-600 {
    --tw-gradient-to: var(--color-amber-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-blue-50 {
    --tw-gradient-to: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-blue-100 {
    --tw-gradient-to: var(--color-blue-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-blue-400 {
    --tw-gradient-to: var(--color-blue-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-blue-500 {
    --tw-gradient-to: var(--color-blue-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-blue-600 {
    --tw-gradient-to: var(--color-blue-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-blue-700 {
    --tw-gradient-to: var(--color-blue-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-blue-800 {
    --tw-gradient-to: var(--color-blue-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-blue-900 {
    --tw-gradient-to: var(--color-blue-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-blue-950 {
    --tw-gradient-to: var(--color-blue-950);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-cyan-500 {
    --tw-gradient-to: var(--color-cyan-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-emerald-50 {
    --tw-gradient-to: var(--color-emerald-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-emerald-700 {
    --tw-gradient-to: var(--color-emerald-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-gray-50 {
    --tw-gradient-to: var(--color-gray-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-gray-100 {
    --tw-gradient-to: var(--color-gray-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-gray-200 {
    --tw-gradient-to: var(--color-gray-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-gray-500 {
    --tw-gradient-to: var(--color-gray-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-gray-900 {
    --tw-gradient-to: var(--color-gray-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-green-800 {
    --tw-gradient-to: var(--color-green-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-indigo-50 {
    --tw-gradient-to: var(--color-indigo-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-indigo-50\/50 {
    --tw-gradient-to: color-mix(in srgb, oklch(96.2% 0.018 272.314) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-indigo-50) 50%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-indigo-100 {
    --tw-gradient-to: var(--color-indigo-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-indigo-200 {
    --tw-gradient-to: var(--color-indigo-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-indigo-500\/10 {
    --tw-gradient-to: color-mix(in srgb, oklch(58.5% 0.233 277.117) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-indigo-500) 10%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-indigo-500\/20 {
    --tw-gradient-to: color-mix(in srgb, oklch(58.5% 0.233 277.117) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-indigo-500) 20%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-indigo-600 {
    --tw-gradient-to: var(--color-indigo-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-indigo-600\/30 {
    --tw-gradient-to: color-mix(in srgb, oklch(51.1% 0.262 276.966) 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-indigo-600) 30%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-indigo-700 {
    --tw-gradient-to: var(--color-indigo-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-indigo-800 {
    --tw-gradient-to: var(--color-indigo-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-indigo-900 {
    --tw-gradient-to: var(--color-indigo-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-orange-100 {
    --tw-gradient-to: var(--color-orange-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-orange-200 {
    --tw-gradient-to: var(--color-orange-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-orange-500 {
    --tw-gradient-to: var(--color-orange-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-pink-50 {
    --tw-gradient-to: var(--color-pink-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-pink-100 {
    --tw-gradient-to: var(--color-pink-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-pink-200 {
    --tw-gradient-to: var(--color-pink-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-pink-500 {
    --tw-gradient-to: var(--color-pink-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-50 {
    --tw-gradient-to: var(--color-purple-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-100 {
    --tw-gradient-to: var(--color-purple-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-400\/15 {
    --tw-gradient-to: color-mix(in srgb, oklch(71.4% 0.203 305.504) 15%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-400) 15%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-500 {
    --tw-gradient-to: var(--color-purple-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-500\/10 {
    --tw-gradient-to: color-mix(in srgb, oklch(62.7% 0.265 303.9) 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-purple-500) 10%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-600 {
    --tw-gradient-to: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-700 {
    --tw-gradient-to: var(--color-purple-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-800 {
    --tw-gradient-to: var(--color-purple-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-red-600 {
    --tw-gradient-to: var(--color-red-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-red-700 {
    --tw-gradient-to: var(--color-red-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-red-800 {
    --tw-gradient-to: var(--color-red-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-teal-100 {
    --tw-gradient-to: var(--color-teal-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-teal-200 {
    --tw-gradient-to: var(--color-teal-200);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-teal-500 {
    --tw-gradient-to: var(--color-teal-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-white {
    --tw-gradient-to: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-yellow-500 {
    --tw-gradient-to: var(--color-yellow-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-yellow-700 {
    --tw-gradient-to: var(--color-yellow-700);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .bg-\[length\:40px_40px\] {
    background-size: 40px 40px;
  }
  .bg-clip-text {
    background-clip: text;
  }
  .bg-center {
    background-position: center;
  }
  .bg-repeat {
    background-repeat: repeat;
  }
  .object-contain {
    object-fit: contain;
  }
  .object-cover {
    object-fit: cover;
  }
  .p-0 {
    padding: calc(var(--spacing) * 0);
  }
  .p-0\.5 {
    padding: calc(var(--spacing) * 0.5);
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-2\.5 {
    padding: calc(var(--spacing) * 2.5);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-5 {
    padding: calc(var(--spacing) * 5);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-7 {
    padding: calc(var(--spacing) * 7);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-3\.5 {
    padding-block: calc(var(--spacing) * 3.5);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }
  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }
  .py-24 {
    padding-block: calc(var(--spacing) * 24);
  }
  .py-28 {
    padding-block: calc(var(--spacing) * 28);
  }
  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }
  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }
  .pt-7\.5 {
    padding-top: calc(var(--spacing) * 7.5);
  }
  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }
  .pt-12 {
    padding-top: calc(var(--spacing) * 12);
  }
  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }
  .pt-20 {
    padding-top: calc(var(--spacing) * 20);
  }
  .pt-24 {
    padding-top: calc(var(--spacing) * 24);
  }
  .pt-28 {
    padding-top: calc(var(--spacing) * 28);
  }
  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }
  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }
  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }
  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }
  .pr-12 {
    padding-right: calc(var(--spacing) * 12);
  }
  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }
  .pb-8 {
    padding-bottom: calc(var(--spacing) * 8);
  }
  .pb-12 {
    padding-bottom: calc(var(--spacing) * 12);
  }
  .pb-16 {
    padding-bottom: calc(var(--spacing) * 16);
  }
  .pb-20 {
    padding-bottom: calc(var(--spacing) * 20);
  }
  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }
  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }
  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }
  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }
  .pl-12 {
    padding-left: calc(var(--spacing) * 12);
  }
  .pl-14 {
    padding-left: calc(var(--spacing) * 14);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .align-bottom {
    vertical-align: bottom;
  }
  .align-middle {
    vertical-align: middle;
  }
  .font-mono {
    font-family: var(--font-geist-mono);
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }
  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .leading-5 {
    --tw-leading: calc(var(--spacing) * 5);
    line-height: calc(var(--spacing) * 5);
  }
  .leading-6 {
    --tw-leading: calc(var(--spacing) * 6);
    line-height: calc(var(--spacing) * 6);
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-extrabold {
    --tw-font-weight: var(--font-weight-extrabold);
    font-weight: var(--font-weight-extrabold);
  }
  .font-light {
    --tw-font-weight: var(--font-weight-light);
    font-weight: var(--font-weight-light);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }
  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }
  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }
  .break-all {
    word-break: break-all;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }
  .text-\[\#5865F2\] {
    color: #5865F2;
  }
  .text-amber-500 {
    color: var(--color-amber-500);
  }
  .text-amber-600 {
    color: var(--color-amber-600);
  }
  .text-amber-700 {
    color: var(--color-amber-700);
  }
  .text-amber-800 {
    color: var(--color-amber-800);
  }
  .text-amber-900 {
    color: var(--color-amber-900);
  }
  .text-black {
    color: var(--color-black);
  }
  .text-blue-100 {
    color: var(--color-blue-100);
  }
  .text-blue-100\/80 {
    color: color-mix(in srgb, oklch(93.2% 0.032 255.585) 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-blue-100) 80%, transparent);
    }
  }
  .text-blue-200 {
    color: var(--color-blue-200);
  }
  .text-blue-300 {
    color: var(--color-blue-300);
  }
  .text-blue-400 {
    color: var(--color-blue-400);
  }
  .text-blue-500 {
    color: var(--color-blue-500);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-blue-700 {
    color: var(--color-blue-700);
  }
  .text-blue-800 {
    color: var(--color-blue-800);
  }
  .text-blue-900 {
    color: var(--color-blue-900);
  }
  .text-emerald-600 {
    color: var(--color-emerald-600);
  }
  .text-emerald-700 {
    color: var(--color-emerald-700);
  }
  .text-emerald-900 {
    color: var(--color-emerald-900);
  }
  .text-gray-200 {
    color: var(--color-gray-200);
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-100 {
    color: var(--color-green-100);
  }
  .text-green-300 {
    color: var(--color-green-300);
  }
  .text-green-400 {
    color: var(--color-green-400);
  }
  .text-green-500 {
    color: var(--color-green-500);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-green-700 {
    color: var(--color-green-700);
  }
  .text-green-800 {
    color: var(--color-green-800);
  }
  .text-indigo-100 {
    color: var(--color-indigo-100);
  }
  .text-indigo-200 {
    color: var(--color-indigo-200);
  }
  .text-indigo-300 {
    color: var(--color-indigo-300);
  }
  .text-indigo-400 {
    color: var(--color-indigo-400);
  }
  .text-indigo-500 {
    color: var(--color-indigo-500);
  }
  .text-indigo-600 {
    color: var(--color-indigo-600);
  }
  .text-indigo-700 {
    color: var(--color-indigo-700);
  }
  .text-indigo-800 {
    color: var(--color-indigo-800);
  }
  .text-indigo-900 {
    color: var(--color-indigo-900);
  }
  .text-orange-500 {
    color: var(--color-orange-500);
  }
  .text-orange-600 {
    color: var(--color-orange-600);
  }
  .text-orange-800 {
    color: var(--color-orange-800);
  }
  .text-purple-100 {
    color: var(--color-purple-100);
  }
  .text-purple-200 {
    color: var(--color-purple-200);
  }
  .text-purple-400 {
    color: var(--color-purple-400);
  }
  .text-purple-500 {
    color: var(--color-purple-500);
  }
  .text-purple-600 {
    color: var(--color-purple-600);
  }
  .text-purple-700 {
    color: var(--color-purple-700);
  }
  .text-purple-800 {
    color: var(--color-purple-800);
  }
  .text-purple-900 {
    color: var(--color-purple-900);
  }
  .text-red-100 {
    color: var(--color-red-100);
  }
  .text-red-400 {
    color: var(--color-red-400);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-red-700 {
    color: var(--color-red-700);
  }
  .text-red-800 {
    color: var(--color-red-800);
  }
  .text-rose-700 {
    color: var(--color-rose-700);
  }
  .text-rose-800 {
    color: var(--color-rose-800);
  }
  .text-rose-900 {
    color: var(--color-rose-900);
  }
  .text-sky-600 {
    color: var(--color-sky-600);
  }
  .text-sky-700 {
    color: var(--color-sky-700);
  }
  .text-teal-100 {
    color: var(--color-teal-100);
  }
  .text-teal-200 {
    color: var(--color-teal-200);
  }
  .text-transparent {
    color: transparent;
  }
  .text-white {
    color: var(--color-white);
  }
  .text-yellow-100 {
    color: var(--color-yellow-100);
  }
  .text-yellow-200 {
    color: var(--color-yellow-200);
  }
  .text-yellow-300 {
    color: var(--color-yellow-300);
  }
  .text-yellow-400 {
    color: var(--color-yellow-400);
  }
  .text-yellow-500 {
    color: var(--color-yellow-500);
  }
  .text-yellow-600 {
    color: var(--color-yellow-600);
  }
  .text-yellow-700 {
    color: var(--color-yellow-700);
  }
  .text-yellow-800 {
    color: var(--color-yellow-800);
  }
  .uppercase {
    text-transform: uppercase;
  }
  .italic {
    font-style: italic;
  }
  .line-through {
    text-decoration-line: line-through;
  }
  .underline {
    text-decoration-line: underline;
  }
  .decoration-emerald-200 {
    text-decoration-color: var(--color-emerald-200);
  }
  .underline-offset-4 {
    text-underline-offset: 4px;
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .placeholder-gray-400 {
    &::placeholder {
      color: var(--color-gray-400);
    }
  }
  .placeholder-gray-500 {
    &::placeholder {
      color: var(--color-gray-500);
    }
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-5 {
    opacity: 5%;
  }
  .opacity-10 {
    opacity: 10%;
  }
  .opacity-20 {
    opacity: 20%;
  }
  .opacity-25 {
    opacity: 25%;
  }
  .opacity-40 {
    opacity: 40%;
  }
  .opacity-50 {
    opacity: 50%;
  }
  .opacity-60 {
    opacity: 60%;
  }
  .opacity-70 {
    opacity: 70%;
  }
  .opacity-75 {
    opacity: 75%;
  }
  .opacity-80 {
    opacity: 80%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-\[0_-1px_3px_rgba\(0\,0\,0\,0\.05\)\] {
    --tw-shadow: 0 -1px 3px var(--tw-shadow-color, rgba(0,0,0,0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-inner {
    --tw-shadow: inset 0 2px 4px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-1 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-amber-200 {
    --tw-shadow-color: oklch(92.4% 0.12 95.746);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, var(--color-amber-200) var(--tw-shadow-alpha), transparent);
    }
  }
  .shadow-blue-50\/50 {
    --tw-shadow-color: color-mix(in srgb, oklch(97% 0.014 254.604) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-50) 50%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }
  .shadow-blue-100\/50 {
    --tw-shadow-color: color-mix(in srgb, oklch(93.2% 0.032 255.585) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-100) 50%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }
  .shadow-blue-200 {
    --tw-shadow-color: oklch(88.2% 0.059 254.128);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, var(--color-blue-200) var(--tw-shadow-alpha), transparent);
    }
  }
  .shadow-gray-200 {
    --tw-shadow-color: oklch(92.8% 0.006 264.531);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, var(--color-gray-200) var(--tw-shadow-alpha), transparent);
    }
  }
  .shadow-green-200 {
    --tw-shadow-color: oklch(92.5% 0.084 155.995);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, var(--color-green-200) var(--tw-shadow-alpha), transparent);
    }
  }
  .shadow-pink-200 {
    --tw-shadow-color: oklch(89.9% 0.061 343.231);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, var(--color-pink-200) var(--tw-shadow-alpha), transparent);
    }
  }
  .shadow-purple-200 {
    --tw-shadow-color: oklch(90.2% 0.063 306.703);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, var(--color-purple-200) var(--tw-shadow-alpha), transparent);
    }
  }
  .shadow-yellow-200 {
    --tw-shadow-color: oklch(94.5% 0.129 101.54);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-shadow-color: color-mix(in oklab, var(--color-yellow-200) var(--tw-shadow-alpha), transparent);
    }
  }
  .ring-black {
    --tw-ring-color: var(--color-black);
  }
  .ring-blue-500 {
    --tw-ring-color: var(--color-blue-500);
  }
  .ring-offset-background {
    --tw-ring-offset-color: var(--background);
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-2xl {
    --tw-blur: blur(var(--blur-2xl));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-3xl {
    --tw-blur: blur(var(--blur-3xl));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-sm {
    --tw-blur: blur(var(--blur-sm));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-xl {
    --tw-blur: blur(var(--blur-xl));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .brightness-100 {
    --tw-brightness: brightness(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .drop-shadow-lg {
    --tw-drop-shadow-size: drop-shadow(0 4px 4px var(--tw-drop-shadow-color, rgb(0 0 0 / 0.15)));
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-lg));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .drop-shadow-sm {
    --tw-drop-shadow-size: drop-shadow(0 1px 2px var(--tw-drop-shadow-color, rgb(0 0 0 / 0.15)));
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-sm));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter-none {
    filter: none;
  }
  .backdrop-blur-md {
    --tw-backdrop-blur: blur(var(--blur-md));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .delay-100 {
    transition-delay: 100ms;
  }
  .delay-200 {
    transition-delay: 200ms;
  }
  .delay-300 {
    transition-delay: 300ms;
  }
  .delay-400 {
    transition-delay: 400ms;
  }
  .delay-500 {
    transition-delay: 500ms;
  }
  .delay-700 {
    transition-delay: 700ms;
  }
  .delay-1000 {
    transition-delay: 1000ms;
  }
  .delay-1500 {
    transition-delay: 1500ms;
  }
  .delay-2000 {
    transition-delay: 2000ms;
  }
  .duration-125 {
    --tw-duration: 125ms;
    transition-duration: 125ms;
  }
  .duration-150 {
    --tw-duration: 150ms;
    transition-duration: 150ms;
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .duration-350 {
    --tw-duration: 350ms;
    transition-duration: 350ms;
  }
  .duration-500 {
    --tw-duration: 500ms;
    transition-duration: 500ms;
  }
  .duration-700 {
    --tw-duration: 700ms;
    transition-duration: 700ms;
  }
  .duration-1000 {
    --tw-duration: 1000ms;
    transition-duration: 1000ms;
  }
  .ease-\[cubic-bezier\(0\.4\,0\,0\.2\,1\)\] {
    --tw-ease: cubic-bezier(0.4,0,0.2,1);
    transition-timing-function: cubic-bezier(0.4,0,0.2,1);
  }
  .ease-\[cubic-bezier\(0\.25\,0\.1\,0\.25\,1\)\] {
    --tw-ease: cubic-bezier(0.25,0.1,0.25,1);
    transition-timing-function: cubic-bezier(0.25,0.1,0.25,1);
  }
  .ease-\[cubic-bezier\(0\.68\,-0\.55\,0\.27\,1\.55\)\] {
    --tw-ease: cubic-bezier(0.68,-0.55,0.27,1.55);
    transition-timing-function: cubic-bezier(0.68,-0.55,0.27,1.55);
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }
  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }
  .group-focus-within\:scale-110 {
    &:is(:where(.group):focus-within *) {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .group-focus-within\:text-blue-600 {
    &:is(:where(.group):focus-within *) {
      color: var(--color-blue-600);
    }
  }
  .group-hover\:w-full {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        width: 100%;
      }
    }
  }
  .group-hover\:-translate-x-1 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-translate-x: calc(var(--spacing) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .group-hover\:-translate-x-1\.5 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-translate-x: calc(var(--spacing) * -1.5);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .group-hover\:-translate-x-2 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-translate-x: calc(var(--spacing) * -2);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .group-hover\:translate-x-0 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-translate-x: calc(var(--spacing) * 0);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .group-hover\:translate-x-1 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-translate-x: calc(var(--spacing) * 1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .group-hover\:translate-x-1\.5 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-translate-x: calc(var(--spacing) * 1.5);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .group-hover\:translate-x-2 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-translate-x: calc(var(--spacing) * 2);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .group-hover\:scale-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-scale-x: 100%;
        --tw-scale-y: 100%;
        --tw-scale-z: 100%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .group-hover\:scale-105 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .group-hover\:scale-110 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-scale-x: 110%;
        --tw-scale-y: 110%;
        --tw-scale-z: 110%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .group-hover\:scale-125 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-scale-x: 125%;
        --tw-scale-y: 125%;
        --tw-scale-z: 125%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .group-hover\:scale-150 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-scale-x: 150%;
        --tw-scale-y: 150%;
        --tw-scale-z: 150%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .group-hover\:-rotate-12 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        rotate: calc(12deg * -1);
      }
    }
  }
  .group-hover\:rotate-3 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        rotate: 3deg;
      }
    }
  }
  .group-hover\:rotate-6 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        rotate: 6deg;
      }
    }
  }
  .group-hover\:rotate-12 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        rotate: 12deg;
      }
    }
  }
  .group-hover\:border-blue-500 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        border-color: var(--color-blue-500);
      }
    }
  }
  .group-hover\:bg-blue-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-blue-100);
      }
    }
  }
  .group-hover\:bg-blue-200 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-blue-200);
      }
    }
  }
  .group-hover\:bg-blue-500 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-blue-500);
      }
    }
  }
  .group-hover\:bg-blue-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-blue-600);
      }
    }
  }
  .group-hover\:bg-green-200 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-green-200);
      }
    }
  }
  .group-hover\:bg-green-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-green-600);
      }
    }
  }
  .group-hover\:bg-indigo-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-indigo-100);
      }
    }
  }
  .group-hover\:bg-indigo-200 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-indigo-200);
      }
    }
  }
  .group-hover\:bg-indigo-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-indigo-600);
      }
    }
  }
  .group-hover\:bg-orange-200 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-orange-200);
      }
    }
  }
  .group-hover\:bg-orange-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-orange-600);
      }
    }
  }
  .group-hover\:bg-purple-200 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-purple-200);
      }
    }
  }
  .group-hover\:bg-red-200 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-red-200);
      }
    }
  }
  .group-hover\:bg-red-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-red-600);
      }
    }
  }
  .group-hover\:bg-gradient-to-r {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-gradient-position: to right in oklab;
        background-image: linear-gradient(var(--tw-gradient-stops));
      }
    }
  }
  .group-hover\:from-blue-700 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-blue-700);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .group-hover\:from-green-300 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-green-300);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .group-hover\:from-purple-300 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-purple-300);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .group-hover\:from-red-300 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-red-300);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .group-hover\:to-blue-800 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-blue-800);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .group-hover\:to-orange-300 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-orange-300);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .group-hover\:to-pink-300 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-pink-300);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .group-hover\:to-teal-300 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-teal-300);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .group-hover\:text-blue-500 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-blue-500);
      }
    }
  }
  .group-hover\:text-blue-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-blue-600);
      }
    }
  }
  .group-hover\:text-blue-700 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-blue-700);
      }
    }
  }
  .group-hover\:text-blue-800 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-blue-800);
      }
    }
  }
  .group-hover\:text-gray-700 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-gray-700);
      }
    }
  }
  .group-hover\:text-gray-800 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-gray-800);
      }
    }
  }
  .group-hover\:text-green-700 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-green-700);
      }
    }
  }
  .group-hover\:text-indigo-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-indigo-600);
      }
    }
  }
  .group-hover\:text-orange-500 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-orange-500);
      }
    }
  }
  .group-hover\:text-orange-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-orange-600);
      }
    }
  }
  .group-hover\:text-orange-700 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-orange-700);
      }
    }
  }
  .group-hover\:text-purple-500 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-purple-500);
      }
    }
  }
  .group-hover\:text-purple-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-purple-600);
      }
    }
  }
  .group-hover\:text-purple-700 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-purple-700);
      }
    }
  }
  .group-hover\:text-red-500 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-red-500);
      }
    }
  }
  .group-hover\:text-red-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-red-600);
      }
    }
  }
  .group-hover\:text-red-700 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-red-700);
      }
    }
  }
  .group-hover\:text-rose-600 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-rose-600);
      }
    }
  }
  .group-hover\:text-teal-700 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-teal-700);
      }
    }
  }
  .group-hover\:text-white {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .group-hover\:opacity-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .group-hover\:shadow-lg {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .group-hover\:shadow-xl {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .file\:mr-4 {
    &::file-selector-button {
      margin-right: calc(var(--spacing) * 4);
    }
  }
  .file\:rounded-full {
    &::file-selector-button {
      border-radius: calc(infinity * 1px);
    }
  }
  .file\:border-0 {
    &::file-selector-button {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
  }
  .file\:bg-blue-50 {
    &::file-selector-button {
      background-color: var(--color-blue-50);
    }
  }
  .file\:px-4 {
    &::file-selector-button {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .file\:py-2 {
    &::file-selector-button {
      padding-block: calc(var(--spacing) * 2);
    }
  }
  .file\:text-sm {
    &::file-selector-button {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .file\:font-semibold {
    &::file-selector-button {
      --tw-font-weight: var(--font-weight-semibold);
      font-weight: var(--font-weight-semibold);
    }
  }
  .file\:text-blue-700 {
    &::file-selector-button {
      color: var(--color-blue-700);
    }
  }
  .last\:border-0 {
    &:last-child {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
  }
  .last\:border-b-0 {
    &:last-child {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0px;
    }
  }
  .focus-within\:border-blue-500 {
    &:focus-within {
      border-color: var(--color-blue-500);
    }
  }
  .focus-within\:shadow-md {
    &:focus-within {
      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .hover\:translate-x-1 {
    &:hover {
      @media (hover: hover) {
        --tw-translate-x: calc(var(--spacing) * 1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .hover\:-translate-y-0\.5 {
    &:hover {
      @media (hover: hover) {
        --tw-translate-y: calc(var(--spacing) * -0.5);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .hover\:-translate-y-1 {
    &:hover {
      @media (hover: hover) {
        --tw-translate-y: calc(var(--spacing) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .hover\:-translate-y-2 {
    &:hover {
      @media (hover: hover) {
        --tw-translate-y: calc(var(--spacing) * -2);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .hover\:scale-105 {
    &:hover {
      @media (hover: hover) {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .hover\:scale-110 {
    &:hover {
      @media (hover: hover) {
        --tw-scale-x: 110%;
        --tw-scale-y: 110%;
        --tw-scale-z: 110%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .hover\:scale-\[1\.01\] {
    &:hover {
      @media (hover: hover) {
        scale: 1.01;
      }
    }
  }
  .hover\:scale-\[1\.02\] {
    &:hover {
      @media (hover: hover) {
        scale: 1.02;
      }
    }
  }
  .hover\:border-0 {
    &:hover {
      @media (hover: hover) {
        border-style: var(--tw-border-style);
        border-width: 0px;
      }
    }
  }
  .hover\:border-blue-100 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-blue-100);
      }
    }
  }
  .hover\:border-blue-200 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-blue-200);
      }
    }
  }
  .hover\:border-blue-300 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-blue-300);
      }
    }
  }
  .hover\:border-blue-400 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-blue-400);
      }
    }
  }
  .hover\:border-blue-500 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-blue-500);
      }
    }
  }
  .hover\:border-blue-700 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-blue-700);
      }
    }
  }
  .hover\:border-gray-300 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-gray-300);
      }
    }
  }
  .hover\:border-green-200 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-green-200);
      }
    }
  }
  .hover\:border-indigo-200 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-indigo-200);
      }
    }
  }
  .hover\:border-indigo-500 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-indigo-500);
      }
    }
  }
  .hover\:border-red-200 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-red-200);
      }
    }
  }
  .hover\:border-teal-400\/50 {
    &:hover {
      @media (hover: hover) {
        border-color: color-mix(in srgb, oklch(77.7% 0.152 181.912) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          border-color: color-mix(in oklab, var(--color-teal-400) 50%, transparent);
        }
      }
    }
  }
  .hover\:bg-\[\#4752c4\] {
    &:hover {
      @media (hover: hover) {
        background-color: #4752c4;
      }
    }
  }
  .hover\:bg-amber-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-amber-200);
      }
    }
  }
  .hover\:bg-amber-600\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, oklch(66.6% 0.179 58.318) 90%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-amber-600) 90%, transparent);
        }
      }
    }
  }
  .hover\:bg-blue-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-50);
      }
    }
  }
  .hover\:bg-blue-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-100);
      }
    }
  }
  .hover\:bg-blue-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-200);
      }
    }
  }
  .hover\:bg-blue-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-600);
      }
    }
  }
  .hover\:bg-blue-600\/30 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, oklch(54.6% 0.245 262.881) 30%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-blue-600) 30%, transparent);
        }
      }
    }
  }
  .hover\:bg-blue-600\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, oklch(54.6% 0.245 262.881) 90%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-blue-600) 90%, transparent);
        }
      }
    }
  }
  .hover\:bg-blue-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-700);
      }
    }
  }
  .hover\:bg-blue-700\/40 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, oklch(48.8% 0.243 264.376) 40%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-blue-700) 40%, transparent);
        }
      }
    }
  }
  .hover\:bg-emerald-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-emerald-100);
      }
    }
  }
  .hover\:bg-gray-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-50);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-gray-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-200);
      }
    }
  }
  .hover\:bg-gray-300 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-300);
      }
    }
  }
  .hover\:bg-gray-600\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, oklch(44.6% 0.03 256.802) 90%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-gray-600) 90%, transparent);
        }
      }
    }
  }
  .hover\:bg-green-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-600);
      }
    }
  }
  .hover\:bg-green-600\/80 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, oklch(62.7% 0.194 149.214) 80%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-green-600) 80%, transparent);
        }
      }
    }
  }
  .hover\:bg-green-600\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, oklch(62.7% 0.194 149.214) 90%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-green-600) 90%, transparent);
        }
      }
    }
  }
  .hover\:bg-green-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-700);
      }
    }
  }
  .hover\:bg-indigo-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-indigo-50);
      }
    }
  }
  .hover\:bg-indigo-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-indigo-700);
      }
    }
  }
  .hover\:bg-orange-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-orange-600);
      }
    }
  }
  .hover\:bg-pink-600\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, oklch(59.2% 0.249 0.584) 90%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-pink-600) 90%, transparent);
        }
      }
    }
  }
  .hover\:bg-purple-600\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, oklch(55.8% 0.288 302.321) 90%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-purple-600) 90%, transparent);
        }
      }
    }
  }
  .hover\:bg-red-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-50);
      }
    }
  }
  .hover\:bg-red-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-100);
      }
    }
  }
  .hover\:bg-red-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-600);
      }
    }
  }
  .hover\:bg-red-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-700);
      }
    }
  }
  .hover\:bg-sky-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-sky-100);
      }
    }
  }
  .hover\:bg-teal-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-teal-600);
      }
    }
  }
  .hover\:bg-teal-600\/40 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, oklch(60% 0.118 184.704) 40%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-teal-600) 40%, transparent);
        }
      }
    }
  }
  .hover\:bg-white {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-white);
      }
    }
  }
  .hover\:bg-white\/10 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, #fff 10%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
        }
      }
    }
  }
  .hover\:bg-white\/20 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, #fff 20%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
        }
      }
    }
  }
  .hover\:bg-white\/30 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, #fff 30%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
        }
      }
    }
  }
  .hover\:bg-yellow-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-yellow-100);
      }
    }
  }
  .hover\:bg-gradient-to-b {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-position: to bottom in oklab;
        background-image: linear-gradient(var(--tw-gradient-stops));
      }
    }
  }
  .hover\:bg-gradient-to-r {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-position: to right in oklab;
        background-image: linear-gradient(var(--tw-gradient-stops));
      }
    }
  }
  .hover\:from-blue-50 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-blue-50);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:from-blue-50\/30 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: color-mix(in srgb, oklch(97% 0.014 254.604) 30%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-gradient-from: color-mix(in oklab, var(--color-blue-50) 30%, transparent);
        }
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:from-blue-100 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-blue-100);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:from-blue-600 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-blue-600);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:from-blue-700 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-blue-700);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:from-gray-200 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-gray-200);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:from-indigo-100 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-indigo-100);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:from-red-600 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-red-600);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:from-red-700 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-from: var(--color-red-700);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-blue-50 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-blue-50);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-blue-100 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-blue-100);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-blue-700 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-blue-700);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-blue-800 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-blue-800);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-gray-300 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-gray-300);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-indigo-100 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-indigo-100);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-indigo-700 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-indigo-700);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-purple-100 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-purple-100);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-red-700 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-red-700);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-red-800 {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-red-800);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:to-white {
    &:hover {
      @media (hover: hover) {
        --tw-gradient-to: var(--color-white);
        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
      }
    }
  }
  .hover\:stroke-blue-500 {
    &:hover {
      @media (hover: hover) {
        stroke: var(--color-blue-500);
      }
    }
  }
  .hover\:stroke-green-500 {
    &:hover {
      @media (hover: hover) {
        stroke: var(--color-green-500);
      }
    }
  }
  .hover\:stroke-orange-500 {
    &:hover {
      @media (hover: hover) {
        stroke: var(--color-orange-500);
      }
    }
  }
  .hover\:stroke-pink-500 {
    &:hover {
      @media (hover: hover) {
        stroke: var(--color-pink-500);
      }
    }
  }
  .hover\:stroke-purple-500 {
    &:hover {
      @media (hover: hover) {
        stroke: var(--color-purple-500);
      }
    }
  }
  .hover\:text-amber-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-amber-800);
      }
    }
  }
  .hover\:text-blue-200 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-200);
      }
    }
  }
  .hover\:text-blue-500 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-500);
      }
    }
  }
  .hover\:text-blue-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-600);
      }
    }
  }
  .hover\:text-blue-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-700);
      }
    }
  }
  .hover\:text-blue-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-800);
      }
    }
  }
  .hover\:text-blue-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-900);
      }
    }
  }
  .hover\:text-emerald-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-emerald-800);
      }
    }
  }
  .hover\:text-gray-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-600);
      }
    }
  }
  .hover\:text-gray-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-700);
      }
    }
  }
  .hover\:text-gray-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-800);
      }
    }
  }
  .hover\:text-gray-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-900);
      }
    }
  }
  .hover\:text-green-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-green-700);
      }
    }
  }
  .hover\:text-green-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-green-800);
      }
    }
  }
  .hover\:text-green-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-green-900);
      }
    }
  }
  .hover\:text-indigo-500 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-indigo-500);
      }
    }
  }
  .hover\:text-indigo-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-indigo-600);
      }
    }
  }
  .hover\:text-indigo-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-indigo-800);
      }
    }
  }
  .hover\:text-indigo-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-indigo-900);
      }
    }
  }
  .hover\:text-orange-500 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-orange-500);
      }
    }
  }
  .hover\:text-orange-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-orange-600);
      }
    }
  }
  .hover\:text-purple-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-purple-700);
      }
    }
  }
  .hover\:text-purple-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-purple-800);
      }
    }
  }
  .hover\:text-red-500 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-500);
      }
    }
  }
  .hover\:text-red-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-600);
      }
    }
  }
  .hover\:text-red-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-700);
      }
    }
  }
  .hover\:text-red-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-800);
      }
    }
  }
  .hover\:text-rose-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-rose-600);
      }
    }
  }
  .hover\:text-white {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .hover\:decoration-emerald-500 {
    &:hover {
      @media (hover: hover) {
        text-decoration-color: var(--color-emerald-500);
      }
    }
  }
  .hover\:decoration-orange-600 {
    &:hover {
      @media (hover: hover) {
        text-decoration-color: var(--color-orange-600);
      }
    }
  }
  .hover\:decoration-rose-600 {
    &:hover {
      @media (hover: hover) {
        text-decoration-color: var(--color-rose-600);
      }
    }
  }
  .hover\:shadow {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-2xl {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-lg {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-md {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-xl {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-blue-100\/50 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow-color: color-mix(in srgb, oklch(93.2% 0.032 255.585) 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-100) 50%, transparent) var(--tw-shadow-alpha), transparent);
        }
      }
    }
  }
  .hover\:shadow-blue-200\/60 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow-color: color-mix(in srgb, oklch(88.2% 0.059 254.128) 60%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-200) 60%, transparent) var(--tw-shadow-alpha), transparent);
        }
      }
    }
  }
  .hover\:shadow-blue-500\/20 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 20%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-500) 20%, transparent) var(--tw-shadow-alpha), transparent);
        }
      }
    }
  }
  .hover\:shadow-blue-500\/25 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 25%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-500) 25%, transparent) var(--tw-shadow-alpha), transparent);
        }
      }
    }
  }
  .hover\:shadow-blue-500\/30 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 30%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-500) 30%, transparent) var(--tw-shadow-alpha), transparent);
        }
      }
    }
  }
  .hover\:shadow-blue-500\/40 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 40%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-blue-500) 40%, transparent) var(--tw-shadow-alpha), transparent);
        }
      }
    }
  }
  .hover\:shadow-indigo-500\/20 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow-color: color-mix(in srgb, oklch(58.5% 0.233 277.117) 20%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-indigo-500) 20%, transparent) var(--tw-shadow-alpha), transparent);
        }
      }
    }
  }
  .hover\:shadow-red-500\/40 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow-color: color-mix(in srgb, oklch(63.7% 0.237 25.331) 40%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-red-500) 40%, transparent) var(--tw-shadow-alpha), transparent);
        }
      }
    }
  }
  .hover\:shadow-teal-500\/20 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow-color: color-mix(in srgb, oklch(70.4% 0.14 182.503) 20%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-teal-500) 20%, transparent) var(--tw-shadow-alpha), transparent);
        }
      }
    }
  }
  .hover\:shadow-white\/20 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow-color: color-mix(in srgb, #fff 20%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-white) 20%, transparent) var(--tw-shadow-alpha), transparent);
        }
      }
    }
  }
  .hover\:shadow-white\/30 {
    &:hover {
      @media (hover: hover) {
        --tw-shadow-color: color-mix(in srgb, #fff 30%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-white) 30%, transparent) var(--tw-shadow-alpha), transparent);
        }
      }
    }
  }
  .hover\:file\:bg-blue-100 {
    &:hover {
      @media (hover: hover) {
        &::file-selector-button {
          background-color: var(--color-blue-100);
        }
      }
    }
  }
  .focus\:border-blue-300 {
    &:focus {
      border-color: var(--color-blue-300);
    }
  }
  .focus\:border-blue-500 {
    &:focus {
      border-color: var(--color-blue-500);
    }
  }
  .focus\:border-indigo-500 {
    &:focus {
      border-color: var(--color-indigo-500);
    }
  }
  .focus\:border-red-500 {
    &:focus {
      border-color: var(--color-red-500);
    }
  }
  .focus\:border-transparent {
    &:focus {
      border-color: transparent;
    }
  }
  .focus\:bg-white {
    &:focus {
      background-color: var(--color-white);
    }
  }
  .focus\:text-blue-500 {
    &:focus {
      color: var(--color-blue-500);
    }
  }
  .focus\:placeholder-gray-400 {
    &:focus {
      &::placeholder {
        color: var(--color-gray-400);
      }
    }
  }
  .focus\:shadow-lg {
    &:focus {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-0 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-1 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-4 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-\[\#5865F2\] {
    &:focus {
      --tw-ring-color: #5865F2;
    }
  }
  .focus\:ring-amber-500 {
    &:focus {
      --tw-ring-color: var(--color-amber-500);
    }
  }
  .focus\:ring-blue-200 {
    &:focus {
      --tw-ring-color: var(--color-blue-200);
    }
  }
  .focus\:ring-blue-300 {
    &:focus {
      --tw-ring-color: var(--color-blue-300);
    }
  }
  .focus\:ring-blue-400 {
    &:focus {
      --tw-ring-color: var(--color-blue-400);
    }
  }
  .focus\:ring-blue-500 {
    &:focus {
      --tw-ring-color: var(--color-blue-500);
    }
  }
  .focus\:ring-indigo-500 {
    &:focus {
      --tw-ring-color: var(--color-indigo-500);
    }
  }
  .focus\:ring-red-500 {
    &:focus {
      --tw-ring-color: var(--color-red-500);
    }
  }
  .focus\:ring-white {
    &:focus {
      --tw-ring-color: var(--color-white);
    }
  }
  .focus\:ring-offset-1 {
    &:focus {
      --tw-ring-offset-width: 1px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\:ring-offset-2 {
    &:focus {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .focus-visible\:ring-2 {
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-visible\:ring-offset-2 {
    &:focus-visible {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus-visible\:outline-none {
    &:focus-visible {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .active\:scale-100 {
    &:active {
      --tw-scale-x: 100%;
      --tw-scale-y: 100%;
      --tw-scale-z: 100%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .disabled\:pointer-events-none {
    &:disabled {
      pointer-events: none;
    }
  }
  .disabled\:cursor-default {
    &:disabled {
      cursor: default;
    }
  }
  .disabled\:cursor-not-allowed {
    &:disabled {
      cursor: not-allowed;
    }
  }
  .disabled\:bg-blue-400 {
    &:disabled {
      background-color: var(--color-blue-400);
    }
  }
  .disabled\:bg-gray-400 {
    &:disabled {
      background-color: var(--color-gray-400);
    }
  }
  .disabled\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .sm\:top-4 {
    @media (width >= 40rem) {
      top: calc(var(--spacing) * 4);
    }
  }
  .sm\:right-4 {
    @media (width >= 40rem) {
      right: calc(var(--spacing) * 4);
    }
  }
  .sm\:-bottom-16 {
    @media (width >= 40rem) {
      bottom: calc(var(--spacing) * -16);
    }
  }
  .sm\:bottom-2 {
    @media (width >= 40rem) {
      bottom: calc(var(--spacing) * 2);
    }
  }
  .sm\:left-2 {
    @media (width >= 40rem) {
      left: calc(var(--spacing) * 2);
    }
  }
  .sm\:left-4 {
    @media (width >= 40rem) {
      left: calc(var(--spacing) * 4);
    }
  }
  .sm\:left-8 {
    @media (width >= 40rem) {
      left: calc(var(--spacing) * 8);
    }
  }
  .sm\:col-span-2 {
    @media (width >= 40rem) {
      grid-column: span 2 / span 2;
    }
  }
  .sm\:mx-0 {
    @media (width >= 40rem) {
      margin-inline: calc(var(--spacing) * 0);
    }
  }
  .sm\:mx-8 {
    @media (width >= 40rem) {
      margin-inline: calc(var(--spacing) * 8);
    }
  }
  .sm\:my-8 {
    @media (width >= 40rem) {
      margin-block: calc(var(--spacing) * 8);
    }
  }
  .sm\:mt-0 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .sm\:mt-4 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 4);
    }
  }
  .sm\:mt-6 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 6);
    }
  }
  .sm\:mt-8 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 8);
    }
  }
  .sm\:mt-10 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 10);
    }
  }
  .sm\:mr-3 {
    @media (width >= 40rem) {
      margin-right: calc(var(--spacing) * 3);
    }
  }
  .sm\:mr-4 {
    @media (width >= 40rem) {
      margin-right: calc(var(--spacing) * 4);
    }
  }
  .sm\:mr-6 {
    @media (width >= 40rem) {
      margin-right: calc(var(--spacing) * 6);
    }
  }
  .sm\:mb-0 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .sm\:mb-2 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 2);
    }
  }
  .sm\:mb-3 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 3);
    }
  }
  .sm\:mb-4 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 4);
    }
  }
  .sm\:mb-6 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 6);
    }
  }
  .sm\:mb-8 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }
  .sm\:mb-10 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 10);
    }
  }
  .sm\:mb-12 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 12);
    }
  }
  .sm\:mb-16 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 16);
    }
  }
  .sm\:ml-2 {
    @media (width >= 40rem) {
      margin-left: calc(var(--spacing) * 2);
    }
  }
  .sm\:ml-3 {
    @media (width >= 40rem) {
      margin-left: calc(var(--spacing) * 3);
    }
  }
  .sm\:ml-4 {
    @media (width >= 40rem) {
      margin-left: calc(var(--spacing) * 4);
    }
  }
  .sm\:block {
    @media (width >= 40rem) {
      display: block;
    }
  }
  .sm\:flex {
    @media (width >= 40rem) {
      display: flex;
    }
  }
  .sm\:hidden {
    @media (width >= 40rem) {
      display: none;
    }
  }
  .sm\:inline {
    @media (width >= 40rem) {
      display: inline;
    }
  }
  .sm\:h-4 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 4);
    }
  }
  .sm\:h-5 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 5);
    }
  }
  .sm\:h-6 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 6);
    }
  }
  .sm\:h-8 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 8);
    }
  }
  .sm\:h-10 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 10);
    }
  }
  .sm\:h-12 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 12);
    }
  }
  .sm\:h-16 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 16);
    }
  }
  .sm\:h-32 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 32);
    }
  }
  .sm\:h-48 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 48);
    }
  }
  .sm\:h-64 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 64);
    }
  }
  .sm\:h-\[350px\] {
    @media (width >= 40rem) {
      height: 350px;
    }
  }
  .sm\:w-4 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 4);
    }
  }
  .sm\:w-5 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 5);
    }
  }
  .sm\:w-6 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 6);
    }
  }
  .sm\:w-7 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 7);
    }
  }
  .sm\:w-8 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 8);
    }
  }
  .sm\:w-10 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 10);
    }
  }
  .sm\:w-12 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 12);
    }
  }
  .sm\:w-16 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 16);
    }
  }
  .sm\:w-32 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 32);
    }
  }
  .sm\:w-64 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 64);
    }
  }
  .sm\:w-auto {
    @media (width >= 40rem) {
      width: auto;
    }
  }
  .sm\:w-full {
    @media (width >= 40rem) {
      width: 100%;
    }
  }
  .sm\:max-w-lg {
    @media (width >= 40rem) {
      max-width: var(--container-lg);
    }
  }
  .sm\:flex-1 {
    @media (width >= 40rem) {
      flex: 1;
    }
  }
  .sm\:grid-cols-2 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .sm\:grid-cols-3 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .sm\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row;
    }
  }
  .sm\:flex-row-reverse {
    @media (width >= 40rem) {
      flex-direction: row-reverse;
    }
  }
  .sm\:items-center {
    @media (width >= 40rem) {
      align-items: center;
    }
  }
  .sm\:items-start {
    @media (width >= 40rem) {
      align-items: flex-start;
    }
  }
  .sm\:justify-between {
    @media (width >= 40rem) {
      justify-content: space-between;
    }
  }
  .sm\:gap-2 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 2);
    }
  }
  .sm\:gap-4 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 4);
    }
  }
  .sm\:gap-6 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 6);
    }
  }
  .sm\:space-y-2 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .sm\:space-y-6 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .sm\:space-y-8 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .sm\:space-x-3 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:self-auto {
    @media (width >= 40rem) {
      align-self: auto;
    }
  }
  .sm\:rounded-3xl {
    @media (width >= 40rem) {
      border-radius: var(--radius-3xl);
    }
  }
  .sm\:p-4 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 4);
    }
  }
  .sm\:p-5 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 5);
    }
  }
  .sm\:p-6 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .sm\:p-8 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 8);
    }
  }
  .sm\:px-3 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 3);
    }
  }
  .sm\:px-4 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .sm\:px-5 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 5);
    }
  }
  .sm\:px-6 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .sm\:px-8 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .sm\:px-12 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 12);
    }
  }
  .sm\:py-2 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 2);
    }
  }
  .sm\:py-3 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 3);
    }
  }
  .sm\:py-4 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 4);
    }
  }
  .sm\:py-8 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 8);
    }
  }
  .sm\:py-12 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 12);
    }
  }
  .sm\:py-16 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 16);
    }
  }
  .sm\:py-20 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 20);
    }
  }
  .sm\:pt-6 {
    @media (width >= 40rem) {
      padding-top: calc(var(--spacing) * 6);
    }
  }
  .sm\:pt-20 {
    @media (width >= 40rem) {
      padding-top: calc(var(--spacing) * 20);
    }
  }
  .sm\:pt-24 {
    @media (width >= 40rem) {
      padding-top: calc(var(--spacing) * 24);
    }
  }
  .sm\:pt-28 {
    @media (width >= 40rem) {
      padding-top: calc(var(--spacing) * 28);
    }
  }
  .sm\:pb-4 {
    @media (width >= 40rem) {
      padding-bottom: calc(var(--spacing) * 4);
    }
  }
  .sm\:pb-8 {
    @media (width >= 40rem) {
      padding-bottom: calc(var(--spacing) * 8);
    }
  }
  .sm\:pb-20 {
    @media (width >= 40rem) {
      padding-bottom: calc(var(--spacing) * 20);
    }
  }
  .sm\:text-left {
    @media (width >= 40rem) {
      text-align: left;
    }
  }
  .sm\:text-2xl {
    @media (width >= 40rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .sm\:text-3xl {
    @media (width >= 40rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .sm\:text-4xl {
    @media (width >= 40rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .sm\:text-5xl {
    @media (width >= 40rem) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .sm\:text-base {
    @media (width >= 40rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .sm\:text-lg {
    @media (width >= 40rem) {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }
  .sm\:text-sm {
    @media (width >= 40rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .sm\:text-xl {
    @media (width >= 40rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .sm\:group-hover\:w-full {
    @media (width >= 40rem) {
      &:is(:where(.group):hover *) {
        @media (hover: hover) {
          width: 100%;
        }
      }
    }
  }
  .md\:right-auto {
    @media (width >= 48rem) {
      right: auto;
    }
  }
  .md\:left-0 {
    @media (width >= 48rem) {
      left: calc(var(--spacing) * 0);
    }
  }
  .md\:col-span-1 {
    @media (width >= 48rem) {
      grid-column: span 1 / span 1;
    }
  }
  .md\:col-span-2 {
    @media (width >= 48rem) {
      grid-column: span 2 / span 2;
    }
  }
  .md\:col-span-4 {
    @media (width >= 48rem) {
      grid-column: span 4 / span 4;
    }
  }
  .md\:mt-0 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .md\:block {
    @media (width >= 48rem) {
      display: block;
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\:h-\[400px\] {
    @media (width >= 48rem) {
      height: 400px;
    }
  }
  .md\:w-1\/2 {
    @media (width >= 48rem) {
      width: calc(1/2 * 100%);
    }
  }
  .md\:w-1\/3 {
    @media (width >= 48rem) {
      width: calc(1/3 * 100%);
    }
  }
  .md\:w-2\/3 {
    @media (width >= 48rem) {
      width: calc(2/3 * 100%);
    }
  }
  .md\:max-w-2xl {
    @media (width >= 48rem) {
      max-width: var(--container-2xl);
    }
  }
  .md\:max-w-3xl {
    @media (width >= 48rem) {
      max-width: var(--container-3xl);
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-3 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-12 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(12, minmax(0, 1fr));
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .md\:items-center {
    @media (width >= 48rem) {
      align-items: center;
    }
  }
  .md\:items-start {
    @media (width >= 48rem) {
      align-items: flex-start;
    }
  }
  .md\:justify-between {
    @media (width >= 48rem) {
      justify-content: space-between;
    }
  }
  .md\:justify-start {
    @media (width >= 48rem) {
      justify-content: flex-start;
    }
  }
  .md\:gap-8 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 8);
    }
  }
  .md\:gap-12 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 12);
    }
  }
  .md\:space-y-0 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .md\:p-5 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 5);
    }
  }
  .md\:p-12 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 12);
    }
  }
  .md\:px-14 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 14);
    }
  }
  .md\:px-16 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 16);
    }
  }
  .md\:py-12 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 12);
    }
  }
  .md\:py-14 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 14);
    }
  }
  .md\:py-16 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 16);
    }
  }
  .md\:py-20 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 20);
    }
  }
  .md\:py-24 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 24);
    }
  }
  .md\:py-28 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 28);
    }
  }
  .md\:text-left {
    @media (width >= 48rem) {
      text-align: left;
    }
  }
  .md\:text-4xl {
    @media (width >= 48rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .md\:text-5xl {
    @media (width >= 48rem) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .md\:text-6xl {
    @media (width >= 48rem) {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }
  .md\:text-base {
    @media (width >= 48rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .md\:text-lg {
    @media (width >= 48rem) {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }
  .lg\:col-span-1 {
    @media (width >= 64rem) {
      grid-column: span 1 / span 1;
    }
  }
  .lg\:col-span-2 {
    @media (width >= 64rem) {
      grid-column: span 2 / span 2;
    }
  }
  .lg\:mx-0 {
    @media (width >= 64rem) {
      margin-inline: calc(var(--spacing) * 0);
    }
  }
  .lg\:mt-0 {
    @media (width >= 64rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .lg\:mb-0 {
    @media (width >= 64rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .lg\:ml-0 {
    @media (width >= 64rem) {
      margin-left: calc(var(--spacing) * 0);
    }
  }
  .lg\:block {
    @media (width >= 64rem) {
      display: block;
    }
  }
  .lg\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\:inline-block {
    @media (width >= 64rem) {
      display: inline-block;
    }
  }
  .lg\:h-3 {
    @media (width >= 64rem) {
      height: calc(var(--spacing) * 3);
    }
  }
  .lg\:w-1\/2 {
    @media (width >= 64rem) {
      width: calc(1/2 * 100%);
    }
  }
  .lg\:w-1\/3 {
    @media (width >= 64rem) {
      width: calc(1/3 * 100%);
    }
  }
  .lg\:w-1\/4 {
    @media (width >= 64rem) {
      width: calc(1/4 * 100%);
    }
  }
  .lg\:w-2\/3 {
    @media (width >= 64rem) {
      width: calc(2/3 * 100%);
    }
  }
  .lg\:w-3 {
    @media (width >= 64rem) {
      width: calc(var(--spacing) * 3);
    }
  }
  .lg\:w-3\/4 {
    @media (width >= 64rem) {
      width: calc(3/4 * 100%);
    }
  }
  .lg\:w-5\/12 {
    @media (width >= 64rem) {
      width: calc(5/12 * 100%);
    }
  }
  .lg\:w-80 {
    @media (width >= 64rem) {
      width: calc(var(--spacing) * 80);
    }
  }
  .lg\:max-w-lg {
    @media (width >= 64rem) {
      max-width: var(--container-lg);
    }
  }
  .lg\:max-w-none {
    @media (width >= 64rem) {
      max-width: none;
    }
  }
  .lg\:grid-cols-2 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-4 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .lg\:flex-row {
    @media (width >= 64rem) {
      flex-direction: row;
    }
  }
  .lg\:items-center {
    @media (width >= 64rem) {
      align-items: center;
    }
  }
  .lg\:justify-between {
    @media (width >= 64rem) {
      justify-content: space-between;
    }
  }
  .lg\:justify-end {
    @media (width >= 64rem) {
      justify-content: flex-end;
    }
  }
  .lg\:justify-start {
    @media (width >= 64rem) {
      justify-content: flex-start;
    }
  }
  .lg\:gap-10 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 10);
    }
  }
  .lg\:gap-12 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 12);
    }
  }
  .lg\:space-x-3 {
    @media (width >= 64rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .lg\:px-3 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 3);
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .lg\:pr-10 {
    @media (width >= 64rem) {
      padding-right: calc(var(--spacing) * 10);
    }
  }
  .lg\:pr-12 {
    @media (width >= 64rem) {
      padding-right: calc(var(--spacing) * 12);
    }
  }
  .lg\:pl-8 {
    @media (width >= 64rem) {
      padding-left: calc(var(--spacing) * 8);
    }
  }
  .lg\:text-left {
    @media (width >= 64rem) {
      text-align: left;
    }
  }
  .lg\:text-5xl {
    @media (width >= 64rem) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .lg\:text-6xl {
    @media (width >= 64rem) {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }
  .lg\:text-base {
    @media (width >= 64rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .xl\:w-1\/2 {
    @media (width >= 80rem) {
      width: calc(1/2 * 100%);
    }
  }
  .xl\:w-5\/12 {
    @media (width >= 80rem) {
      width: calc(5/12 * 100%);
    }
  }
  .xl\:grid-cols-3 {
    @media (width >= 80rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .xl\:grid-cols-5 {
    @media (width >= 80rem) {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }
  .dark\:scale-0 {
    @media (prefers-color-scheme: dark) {
      --tw-scale-x: 0%;
      --tw-scale-y: 0%;
      --tw-scale-z: 0%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .dark\:scale-100 {
    @media (prefers-color-scheme: dark) {
      --tw-scale-x: 100%;
      --tw-scale-y: 100%;
      --tw-scale-z: 100%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .dark\:-rotate-90 {
    @media (prefers-color-scheme: dark) {
      rotate: calc(90deg * -1);
    }
  }
  .dark\:rotate-0 {
    @media (prefers-color-scheme: dark) {
      rotate: 0deg;
    }
  }
  .dark\:border-gray-700 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-gray-700);
    }
  }
  .dark\:border-gray-800 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-gray-800);
    }
  }
  .dark\:bg-gray-700 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-gray-700);
    }
  }
  .dark\:bg-gray-800 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-gray-800);
    }
  }
  .dark\:bg-gray-900 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-gray-900);
    }
  }
  .dark\:text-gray-400 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-gray-400);
    }
  }
  .dark\:text-white {
    @media (prefers-color-scheme: dark) {
      color: var(--color-white);
    }
  }
  .dark\:hover\:text-white {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-white);
        }
      }
    }
  }
}
:root {
  --background: #ffffff;
  --foreground: #171717;
}
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
.arrow-wrapper {
  display: inline-flex;
  align-items: center;
  transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}
.group:hover .arrow-wrapper {
  transform: translateX(8px);
}
.arrow-wrapper svg {
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}
.group:hover .arrow-wrapper svg {
  transform: scale(1.1);
}
.perspective-1000 {
  perspective: 1000px;
}
.preserve-3d {
  transform-style: preserve-3d;
}
.cube {
  transition: transform 1s ease;
}
.scene:hover .cube-rotate {
  transform: rotateY(180deg) rotateX(45deg);
}
.cube-face {
  backface-visibility: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.5s ease, box-shadow 0.5s ease;
}
.cube-face-front {
  transform: translateZ(128px);
}
.cube-face-back {
  transform: rotateY(180deg) translateZ(128px);
}
.cube-face-right {
  transform: rotateY(90deg) translateZ(128px);
}
.cube-face-left {
  transform: rotateY(-90deg) translateZ(128px);
}
.cube-face-top {
  transform: rotateX(90deg) translateZ(128px);
}
.cube-face-bottom {
  transform: rotateX(-90deg) translateZ(128px);
}
.animate-fade-in {
  animation: fadeIn 0.8s ease-in-out;
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes pulse {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 1;
  }
}
@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
@keyframes countUp {
  0% {
    content: "0";
  }
  20% {
    content: "1";
  }
  40% {
    content: "3";
  }
  60% {
    content: "5";
  }
  100% {
    content: "6";
  }
}
.animate-[countUp_1s_ease-in-out_forwards]::after {
  content: "0";
  animation: countUp 1s ease-in-out forwards;
}
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
.animate-fadeIn {
  animation: scaleIn 0.2s ease-out forwards;
}
.transition-transform {
  transition: transform 0.3s ease;
}
.hover\:transform:hover {
  transform: scale(1.05);
}
input, textarea, select {
  color: #333 !important;
  background-color: white !important;
}
input::placeholder, textarea::placeholder {
  color: #9ca3af !important;
  opacity: 1 !important;
}
select {
  appearance: menulist !important;
  padding-right: 2rem !important;
}
select option {
  color: #333 !important;
  background-color: white !important;
}
@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}
.animate-typing {
  animation: typing 8s steps(40, end) infinite;
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}
@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
    }
  }
}

